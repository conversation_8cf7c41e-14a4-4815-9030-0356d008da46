@echo off
echo ==============================================
echo BOA Control Max - 3ds Max 集成设置
echo ==============================================
echo.

REM 检查3ds Max路径是否存在
if not exist "C:\Program Files\Autodesk\3ds Max 2024" (
  echo 错误: 未找到3ds Max 2024安装路径
  echo 请确认3ds Max已安装，或修改此脚本以使用正确的路径
  goto :eof
)

REM 创建3ds Max脚本目录（如果不存在）
set MAX_SCRIPTS_DIR="C:\Program Files\Autodesk\3ds Max 2024\scripts"
if not exist %MAX_SCRIPTS_DIR% mkdir %MAX_SCRIPTS_DIR%

echo 正在复制启动脚本到3ds Max脚本目录...

REM 获取当前目录
set CURRENT_DIR=%cd%

REM 复制Python启动脚本到3ds Max
copy "%CURRENT_DIR%\start_max_server.py" %MAX_SCRIPTS_DIR%\BOA_start_max_server.py
if errorlevel 1 (
  echo 警告: 无法复制Python启动脚本，可能需要管理员权限
) else (
  echo Python启动脚本已复制到 %MAX_SCRIPTS_DIR%\BOA_start_max_server.py
)

REM 复制MaxScript启动脚本到3ds Max
copy "%CURRENT_DIR%\start_max_server.ms" %MAX_SCRIPTS_DIR%\BOA_start_max_server.ms
if errorlevel 1 (
  echo 警告: 无法复制MaxScript启动脚本，可能需要管理员权限
) else (
  echo MaxScript启动脚本已复制到 %MAX_SCRIPTS_DIR%\BOA_start_max_server.ms
)

REM 更新启动脚本中的路径
echo 正在更新启动脚本中的路径...

REM 为Python脚本创建临时文件
set "TEMP_PY_FILE=%TEMP%\temp_py_script.py"
echo # 3ds Max 与 BOA Control Max 连接的启动脚本 > "%TEMP_PY_FILE%"
echo # 将此脚本保存在任意位置，然后在 3ds Max 的 Python 脚本编辑器中运行 >> "%TEMP_PY_FILE%"
echo. >> "%TEMP_PY_FILE%"
echo import os >> "%TEMP_PY_FILE%"
echo import sys >> "%TEMP_PY_FILE%"
echo from pymxs import runtime as rt >> "%TEMP_PY_FILE%"
echo. >> "%TEMP_PY_FILE%"
echo # 在这里修改为您的实际脚本路径 >> "%TEMP_PY_FILE%"
echo SERVER_SCRIPT_PATH = r"%CURRENT_DIR%\packages\max-server\src\max-utils\maxPyTcpServer.py" >> "%TEMP_PY_FILE%"
echo. >> "%TEMP_PY_FILE%"
echo def start_tcp_server(): >> "%TEMP_PY_FILE%"
echo     """启动 3ds Max TCP 服务器""" >> "%TEMP_PY_FILE%"
echo     if os.path.exists(SERVER_SCRIPT_PATH): >> "%TEMP_PY_FILE%"
echo         try: >> "%TEMP_PY_FILE%"
echo             print("正在启动 3ds Max TCP 服务器...") >> "%TEMP_PY_FILE%"
echo             # 使用 Python 的 execfile 功能执行脚本 >> "%TEMP_PY_FILE%"
echo             exec(open(SERVER_SCRIPT_PATH).read()) >> "%TEMP_PY_FILE%"
echo             print("TCP 服务器脚本已执行") >> "%TEMP_PY_FILE%"
echo         except Exception as e: >> "%TEMP_PY_FILE%"
echo             print(f"启动服务器时出错: {str(e)}") >> "%TEMP_PY_FILE%"
echo     else: >> "%TEMP_PY_FILE%"
echo         print(f"错误: 找不到服务器脚本，请检查路径: {SERVER_SCRIPT_PATH}") >> "%TEMP_PY_FILE%"
echo         print("请修改此脚本中的 SERVER_SCRIPT_PATH 变量以指向正确的文件位置") >> "%TEMP_PY_FILE%"
echo. >> "%TEMP_PY_FILE%"
echo if __name__ == "__main__": >> "%TEMP_PY_FILE%"
echo     start_tcp_server() >> "%TEMP_PY_FILE%"

REM 用正确路径覆盖Python脚本
copy "%TEMP_PY_FILE%" "%CURRENT_DIR%\start_max_server.py"
copy "%TEMP_PY_FILE%" %MAX_SCRIPTS_DIR%\BOA_start_max_server.py

REM 为MaxScript脚本创建临时文件
set "TEMP_MS_FILE=%TEMP%\temp_ms_script.ms"
echo -- 3ds Max 与 BOA Control Max 连接的 MaxScript 启动脚本 > "%TEMP_MS_FILE%"
echo -- 将此脚本保存在任意位置，然后在 3ds Max 中运行 >> "%TEMP_MS_FILE%"
echo. >> "%TEMP_MS_FILE%"
echo fn startMaxServer = ( >> "%TEMP_MS_FILE%"
echo     -- 在这里修改为您的实际脚本路径 >> "%TEMP_MS_FILE%"
echo     serverScriptPath = "%CURRENT_DIR:\=\\%\\packages\\max-server\\src\\max-utils\\maxPyTcpServer.py" >> "%TEMP_MS_FILE%"
echo. >> "%TEMP_MS_FILE%"
echo     if doesFileExist serverScriptPath then ( >> "%TEMP_MS_FILE%"
echo         print "正在启动 3ds Max TCP 服务器..." >> "%TEMP_MS_FILE%"
echo. >> "%TEMP_MS_FILE%"
echo         -- 使用 Python 接口执行脚本 >> "%TEMP_MS_FILE%"
echo         python.ExecuteFile serverScriptPath >> "%TEMP_MS_FILE%"
echo. >> "%TEMP_MS_FILE%"
echo         print "TCP 服务器脚本已执行" >> "%TEMP_MS_FILE%"
echo     ) else ( >> "%TEMP_MS_FILE%"
echo         print ("错误: 找不到服务器脚本，请检查路径: " + serverScriptPath) >> "%TEMP_MS_FILE%"
echo         print "请修改此脚本中的 serverScriptPath 变量以指向正确的文件位置" >> "%TEMP_MS_FILE%"
echo     ) >> "%TEMP_MS_FILE%"
echo ) >> "%TEMP_MS_FILE%"
echo. >> "%TEMP_MS_FILE%"
echo -- 执行启动函数 >> "%TEMP_MS_FILE%"
echo startMaxServer() >> "%TEMP_MS_FILE%"

REM 用正确路径覆盖MaxScript脚本
copy "%TEMP_MS_FILE%" "%CURRENT_DIR%\start_max_server.ms"
copy "%TEMP_MS_FILE%" %MAX_SCRIPTS_DIR%\BOA_start_max_server.ms

REM 清理临时文件
del "%TEMP_PY_FILE%"
del "%TEMP_MS_FILE%"

echo.
echo ==============================================
echo 设置完成! 请按照以下步骤在3ds Max中启动服务器:
echo ==============================================
echo.
echo 1. 启动HTTP服务器:
echo    在命令行中执行: npx pnpm run dev
echo.
echo 2. 在3ds Max中启动TCP服务器:
echo    - 打开3ds Max 2024
echo    - 从主菜单选择 MAXScript -^> Run Script...
echo    - 选择 C:\Program Files\Autodesk\3ds Max 2024\scripts\BOA_start_max_server.ms
echo.
echo 或者可以使用Python启动:
echo    - 打开3ds Max脚本编辑器(按F11)
echo    - 切换到Python模式
echo    - 输入: exec(open(r"C:\Program Files\Autodesk\3ds Max 2024\scripts\BOA_start_max_server.py").read())
echo.
echo 有关详细说明，请参阅 安装说明.md 文件
echo ==============================================

pause 