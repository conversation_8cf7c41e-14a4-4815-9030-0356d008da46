import socket
import json

# 创建socket连接
sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)

try:
    # 连接到服务器
    print("正在连接到Max服务器...")
    sock.connect(('localhost', 8123))
    
    # 发送简单命令
    command = {
        "lang": "mxs",
        "code": "box()"
    }
    
    print("发送命令：创建方块")
    sock.sendall(json.dumps(command).encode('utf-8'))
    
    # 接收响应
    print("等待响应...")
    response = sock.recv(8192)
    print("服务器响应:", response.decode('utf-8'))
    
except Exception as e:
    print("错误:", str(e))
finally:
    sock.close()
    
input("按Enter键退出...") 