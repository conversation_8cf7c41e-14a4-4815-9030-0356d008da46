# 中文环境编码问题解决方案

## 问题描述

在中文版 Windows 系统中运行 3ds Max Python 脚本时，常常会遇到以下编码错误：

```
'gbk' codec can't decode byte 0xb9 in position 51: illegal multibyte sequence
```

这是因为在中文 Windows 系统中，Python 默认使用 GBK 编码读取文件，但脚本文件可能是以 UTF-8 编码保存的。

## 解决方案

### 方法 1：使用我们提供的编码修复版本启动脚本

我们已经创建了针对中文环境的启动脚本，明确指定了 UTF-8 编码。请使用以下文件之一：

- `直接启动TCP服务器.py` - Python 版解决方案
- `启动TCP服务器.ms` - MaxScript 版解决方案（内部调用 Python 脚本）

### 方法 2：如何修复您自己的 Python 脚本

如果您想修改自己的 Python 脚本以解决编码问题，请按照以下步骤操作：

1. 打开您的 Python 脚本
2. 找到所有使用 `open()` 函数读取文件的地方
3. 将代码从：
   ```python
   open(file_path, 'r').read()
   ```
   修改为：
   ```python
   open(file_path, 'r', encoding='utf-8').read()
   ```

4. 或更好的写法是使用 `with` 语句：
   ```python
   with open(file_path, 'r', encoding='utf-8') as f:
       content = f.read()
   ```

### 方法 3：修改 Python 默认编码（不推荐）

这种方法可能影响其他 Python 程序，不推荐使用，但在某些情况下可能有用：

```python
import sys
sys.setdefaultencoding('utf-8')  # 注意：在 Python 3 中此方法已被移除
```

## 为什么会出现这个问题？

中文版 Windows 系统默认使用 GBK 编码作为系统编码。当 Python 在这样的系统上运行时，如果没有明确指定编码，它会使用系统默认的 GBK 编码尝试读取文件。

如果文件是以 UTF-8 编码保存的（大多数现代代码编辑器的默认选择），就会导致编码不匹配错误。

## 预防措施

1. 始终使用 UTF-8 编码保存所有 Python 文件
2. 在读取文件时，始终明确指定编码
3. 在脚本开头添加编码声明（适用于 Python 2）：
   ```python
   # -*- coding: utf-8 -*-
   ```

4. 对于 MaxScript 调用 Python 脚本的情况，确保中间脚本使用正确的编码处理 