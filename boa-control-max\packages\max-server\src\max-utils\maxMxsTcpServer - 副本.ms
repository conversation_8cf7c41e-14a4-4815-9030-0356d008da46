clearListener()

-- 创建服务器结构
struct TCPServer (
    -- 公共属性
    port = 8123,
    
    -- 私有成员
    private tcpListener = undefined,
    private clientSocket = undefined,
    private isRunning = false,
    
    -- 启动服务器
    fn start = (
        try (
            -- 如果已经在运行，先停止
            if isRunning do (
                format "服务器已在运行，正在重新启动...\n"
                stop()
            )
            
            -- 创建监听器
            local ipAddress = (dotNetClass "System.Net.IPAddress").Parse "127.0.0.1"
            tcpListener = dotNetObject "System.Net.Sockets.TcpListener" ipAddress port
            tcpListener.Start()
            isRunning = true
            
            format "服务器已启动在端口 %\n" port
            messageBox ("MaxMCP服务器已启动!\n监听地址: 127.0.0.1:" + port as string)
            true
        ) catch (
            local errorMsg = "启动服务器失败: " + (getCurrentException())
            format "%\n" errorMsg
            messageBox errorMsg title:"错误"
            isRunning = false
            false
        )
    ),
    
    -- 停止服务器
    fn stop = (
        isRunning = false
        
        if tcpListener != undefined do (
            try (
                tcpListener.Stop()
            ) catch (
                format "停止监听器时出错: %\n" (getCurrentException())
            )
            tcpListener = undefined
        )
        
        if clientSocket != undefined do (
            try (
                clientSocket.Close()
            ) catch (
                format "关闭客户端连接时出错: %\n" (getCurrentException())
            )
            clientSocket = undefined
        )
        
        format "服务器已停止\n"
    ),
    
    -- 处理客户端请求
    fn handleClient = (
        try (
            -- 等待客户端连接
            format "等待客户端连接...\n"
            clientSocket = tcpListener.AcceptSocket()
            format "客户端已连接\n"
            
            -- 接收数据
            local buffer = dotNetObject "System.Byte[]" 4096
            local bytesRead = clientSocket.Receive buffer
            
            if bytesRead > 0 then (
                -- 转换为字符串
                local encoding = dotNetClass "System.Text.UTF8Encoding"
                local command = trimright (encoding.UTF8.GetString buffer 0 bytesRead)
                format "收到命令: %\n" command
                
                -- 执行命令
                local result = undefined
                try (
                    result = execute command
                    format "命令执行成功: %\n" (result as string)
                ) catch (
                    local errorMsg = "Error: " + (getCurrentException())
                    format "命令执行失败: %\n" errorMsg
                    result = errorMsg
                )
                
                -- 发送响应
                local response = result as string
                local responseBytes = encoding.UTF8.GetBytes response
                clientSocket.Send responseBytes
                format "已发送响应: %\n" response
            )
            
            -- 关闭连接
            clientSocket.Close()
            clientSocket = undefined
            format "客户端连接已关闭\n"
            
        ) catch (
            format "处理客户端时出错: %\n" (getCurrentException())
            if clientSocket != undefined do (
                try (
                    clientSocket.Close()
                ) catch ()
                clientSocket = undefined
            )
        )
    ),
    
    -- 主循环
    fn run = (
        while isRunning do (
            if tcpListener != undefined then (
                handleClient()
            ) else (
                format "服务器已停止运行\n"
                exit
            )
        )
    )
)

-- 停止现有服务器
if globalTCPServer != undefined do (
    globalTCPServer.stop()
)

-- 创建并启动新服务器
global globalTCPServer = TCPServer()
if globalTCPServer.start() then (
    -- 在新线程中运行服务器
    dotNet.launchMonoScript "TCPServerThread" "globalTCPServer.run()"
)
