import socket
import json
import time

def test_max_connection(host='localhost', port=8123):
    print("开始测试Max服务器连接...")
    
    # 创建socket连接
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    try:
        # 连接服务器
        print(f"正在连接服务器 {host}:{port}...")
        sock.connect((host, port))
        print("连接成功！")
        
        # 发送测试命令 - 创建一个红色方块
        test_command = {
            "lang": "mxs",
            "code": "box length:50 width:50 height:50 wirecolor:red"
        }
        
        # 发送命令
        print("发送测试命令：创建红色方块...")
        sock.sendall(json.dumps(test_command).encode('utf-8'))
        
        # 等待响应
        print("等待服务器响应...")
        response = sock.recv(8192)
        result = json.loads(response.decode('utf-8'))
        
        print("\n服务器响应:")
        print(json.dumps(result, indent=2, ensure_ascii=False))
        
        if "error" not in result:
            print("\n测试成功！请检查3ds Max中是否创建了一个红色方块。")
        else:
            print(f"\n测试失败：{result['error']}")
            
    except Exception as e:
        print(f"\n测试出错：{str(e)}")
    finally:
        sock.close()

if __name__ == "__main__":
    test_max_connection()
    input("\n按Enter键退出...") 