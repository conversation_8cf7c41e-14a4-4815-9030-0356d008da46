-- BOA Control Max 服务器启动脚本
-- 在3ds Max中运行此脚本启动TCP服务器

fn startMaxServer = 
(
    -- 定义Python服务器脚本的路径
    serverPath = (getFilenamePath (getThisScriptFilename())) + "maxPyTcpServer.py"
    
    if doesFileExist serverPath then
    (
        print "正在启动MaxMCP服务器..."
        try
        (
            python.ExecuteFile serverPath
            print "服务器启动脚本执行完成"
        )
        catch
        (
            errorMessage = getCurrentException()
            messageBox ("启动服务器时出错: " + errorMessage) title:"错误"
            print ("错误: " + errorMessage)
        )
    )
    else
    (
        messageBox ("找不到服务器脚本: " + serverPath) title:"文件未找到"
        print ("错误: 找不到文件 " + serverPath)
    )
)

-- 执行启动函数
startMaxServer() 