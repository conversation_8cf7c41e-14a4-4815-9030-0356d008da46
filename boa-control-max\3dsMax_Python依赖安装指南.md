# 3ds Max Python 依赖安装指南

本指南将帮助您在 3ds Max 的 Python 环境中安装所需的依赖模块。

## 方法一：使用修改后的脚本（推荐）

我们已经修改了 `maxPyTcpServer.py` 脚本，移除了对 `requests` 模块的依赖。使用修改后的脚本，您应该能够直接在 3ds Max 中运行脚本而不需要额外安装依赖。

请使用新创建的 `Max_TCP_Server.py` 脚本在 3ds Max 中启动服务器。

## 方法二：手动安装缺失的 Python 模块

如果您需要在 3ds Max 中安装 Python 模块（例如 `requests`），请按照以下步骤操作：

### 1. 确定 3ds Max 的 Python 路径

3ds Max 2024 使用内置的 Python 环境，通常位于：
```
C:\Program Files\Autodesk\3ds Max 2024\Python
```

### 2. 使用 pip 安装模块

您可以通过以下方式安装模块：

1. 以管理员身份打开命令提示符
2. 导航到 3ds Max 的 Python 目录：
   ```
   cd "C:\Program Files\Autodesk\3ds Max 2024\Python"
   ```
3. 使用 pip 安装缺失的模块：
   ```
   .\python.exe -m pip install requests
   ```

### 3. 验证安装

在 3ds Max 的 Python 脚本编辑器中，尝试导入模块：
```python
import sys
print(sys.path)
import requests
print(requests.__version__)
```

如果没有错误，表示模块已成功安装。

## 方法三：在不修改脚本的情况下运行

如果您不想修改脚本或安装模块，可以创建一个不依赖于 `requests` 的简化版本服务器。我们已经提供了这样的脚本：`Max_TCP_Server.py`。

## 故障排除

### 常见错误

1. **ModuleNotFoundError: No module named 'requests'**
   - 原因：缺少 `requests` 模块
   - 解决方案：使用方法一（修改后的脚本）或方法二（安装模块）

2. **权限错误**
   - 原因：没有足够的权限安装模块
   - 解决方案：以管理员身份运行命令提示符

3. **导入错误**
   - 原因：Python 路径问题
   - 解决方案：检查 `sys.path` 确保 Python 能找到正确的模块路径

### 其他可能需要的模块

如果您遇到其他模块缺失的错误，可以使用相同的方法安装：
```
.\python.exe -m pip install [模块名]
```

## 建议的启动流程

1. 启动 HTTP 服务器：
   ```
   cd D:\AI\MCP_MAX\boa-control-max
   npx pnpm run dev
   ```

2. 在 3ds Max 中启动 TCP 服务器：
   - 打开 3ds Max 2024
   - 打开 Python 脚本编辑器 (F11)
   - 加载并运行 `D:\AI\MCP_MAX\boa-control-max\Max_TCP_Server.py` 