# 3ds Max BOA Control Max TCP服务器直接启动脚本
# 此版本解决了中文编码问题
# 在3ds Max的Python脚本编辑器中运行此脚本

import os
import sys
from pymxs import runtime as rt

# 服务器脚本路径
TCP_SERVER_PATH = r"D:\AI\MCP_MAX\boa-control-max\packages\max-server\src\max-utils\maxPyTcpServer.py"

def start_server():
    print(f"正在加载TCP服务器脚本: {TCP_SERVER_PATH}")
    
    if not os.path.exists(TCP_SERVER_PATH):
        error_msg = f"错误: 找不到脚本文件 {TCP_SERVER_PATH}"
        print(error_msg)
        rt.messageBox(error_msg, title="错误")
        return
    
    try:
        # 使用 UTF-8 编码读取文件，解决中文编码问题
        with open(TCP_SERVER_PATH, 'r', encoding='utf-8') as file:
            script_content = file.read()
        
        # 在全局命名空间执行脚本
        exec(script_content, globals())
        
        # 成功消息
        print("TCP服务器脚本已成功加载!")
        rt.messageBox("TCP服务器已成功启动!\n现在可以使用 HTTP 客户端连接到 3ds Max。", title="BOA Control Max")
    
    except Exception as e:
        error_msg = f"执行脚本时出错: {str(e)}"
        print(error_msg)
        rt.messageBox(error_msg, title="错误")

# 执行启动
start_server() 