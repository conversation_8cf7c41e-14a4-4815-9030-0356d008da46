# BOA Control Max TCP通信测试工具使用说明

## 概述

BOA Control Max是一个通过TCP和HTTP端点在3ds Max中执行MaxScript和Python代码的桥接工具。本测试工具包提供了多种方式来测试与3ds Max的通信。

## 环境要求

- 3ds Max已安装并运行
- Python 3.7+已安装（无需额外模块）
- 已在3ds Max中执行TCP服务器启动脚本

## 测试工具列表

本工具包含以下测试工具：

1. **test-tcp-box.py** - TCP直连测试脚本，创建红色盒子
2. **max-command.py** - 命令行通用TCP命令发送工具
3. **测试创建几何体.bat** - 交互式菜单批处理，可执行多种测试
4. **tcp-test.html** - 网页界面演示（仅UI示例，浏览器无法直接连接TCP）

## 使用步骤

### 第一步：启动服务器

1. 打开3ds Max
2. 在MaxScript编辑器中加载并运行`启动TCP服务器.ms`
3. 确认看到"MaxMCP简化版服务器已启动，监听地址：localhost:8123"的消息

### 第二步：选择测试工具

#### 方法一：使用菜单批处理

1. 双击运行`测试创建几何体.bat`
2. 在菜单中选择1-7的操作选项
3. 观察3ds Max场景中的变化

#### 方法二：使用命令行工具

执行自定义MaxScript命令：
```
python max-command.py "box length:100 width:100 height:100 wirecolor:green"
```

执行Python命令：
```
python max-command.py "rt.sphere(radius=75)" py
```

#### 方法三：直接创建红色盒子

执行测试脚本：
```
python test-tcp-box.py
```

## 常见问题

1. **连接错误**：确保3ds Max已启动且TCP服务器已运行，检查端口是否为8123
2. **乱码问题**：确保所有文件使用UTF-8编码
3. **命令执行失败**：检查MaxScript语法是否正确

## HTTP服务器说明

原版BOA Control Max支持HTTP服务器，但简化版仅实现了TCP服务器功能。HTTP请求将无法工作，这就是为什么在浏览器中测试时会出现404错误。

## 高级用法

### 自定义对象

要创建自定义对象，可以使用MaxScript代码，例如：

```
python max-command.py "teapot radius:60 wirecolor:(color 255 0 255)"
```

### 场景操作

```
python max-command.py "max zoomextents all"  // 缩放视图
python max-command.py "max select $*"        // 选择所有对象
python max-command.py "delete objects"       // 删除所有对象
```

## 源代码说明

- `简化版TCP服务器.py`：主服务器实现，在3ds Max中运行
- `启动TCP服务器.ms`：启动服务器的MaxScript脚本
- 测试工具源码也可以作为学习和开发自己的TCP客户端的示例 