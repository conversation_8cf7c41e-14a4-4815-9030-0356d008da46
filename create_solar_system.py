import json
import requests
import math

def create_solar_system():
    # MCP服务地址
    MCP_SERVER = "http://localhost:3000"
    
    # 创建场景前先重置
    reset_code = """
resetMaxFile #noPrompt
"""
    response = requests.post(f"{MCP_SERVER}/execute", json={
        "code": reset_code,
        "type": "maxscript"
    })
    
    # 创建太阳
    sun_code = """
sun = Sphere radius:20 segments:32
sun.name = "Sun"
sun.position = [0, 0, 0]

-- 创建太阳材质
sun_material = StandardMaterial()
sun_material.diffuse = (color 255 255 0)
sun_material.selfIllumAmount = 100
sun.material = sun_material
"""
    
    # Create a yellow material for the sun
    sun_material = rt.StandardMaterial()
    sun_material.diffuse = rt.Color(255, 255, 0)
    sun_material.selfIllumAmount = 100
    sun.material = sun_material

    # Create Earth
    earth = rt.Sphere(radius=8, segments=32)
    earth.name = "Earth"
    earth.pos = rt.Point3(100, 0, 0)
    
    # Create a blue material for Earth
    earth_material = rt.StandardMaterial()
    earth_material.diffuse = rt.Color(0, 100, 255)
    earth.material = earth_material
    
    # Create Moon
    moon = rt.Sphere(radius=3, segments=32)
    moon.name = "Moon"
    moon.pos = rt.Point3(130, 0, 0)
    
    # Create a grey material for Moon
    moon_material = rt.StandardMaterial()
    moon_material.diffuse = rt.Color(200, 200, 200)
    moon.material = moon_material
    
    # Set up animation
    rt.animationRange = rt.Interval(0, rt.timeValue(300))
    
    # Animate Earth's rotation around Sun
    with rt.holdMaxFile():
        for frame in range(0, 301, 1):
            time = rt.timeValue(frame)
            angle = (frame / 300.0) * 2 * math.pi
            
            # Earth orbits around Sun
            earth_x = 100 * math.cos(angle)
            earth_z = 100 * math.sin(angle)
            rt.setKeyMode(True)
            earth.pos = rt.Point3(earth_x, 0, earth_z)
            rt.setKeyTime(time)
            
            # Moon orbits around Earth
            moon_x = earth_x + 30 * math.cos(angle * 3)  # Moon rotates 3 times faster
            moon_z = earth_z + 30 * math.sin(angle * 3)
            moon.pos = rt.Point3(moon_x, 0, moon_z)
            rt.setKeyTime(time)
    
    # Add a camera
    camera = rt.FreeCamera()
    camera.name = "SolarSystemCamera"
    camera.pos = rt.Point3(0, -300, 200)
    camera.target = rt.Point3(0, 0, 0)
    
    # Create ambient light
    omni = rt.Omnilight()
    omni.name = "SunLight"
    omni.pos = rt.Point3(0, 0, 0)
    omni.multiplier = 2
    
    return {
        "message": "Solar system created successfully",
        "objects": ["Sun", "Earth", "Moon", "SolarSystemCamera", "SunLight"]
    }
