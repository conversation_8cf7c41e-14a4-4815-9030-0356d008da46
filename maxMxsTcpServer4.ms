-- maxMxsTcpServer2.ms
-- Optimized to handle HTTP POST requests, nested JSON, Python code, and improve MCP compatibility

clearListener()

-- 提取 HTTP 请求的 JSON 体
fn parseHttpRequest requestStr = (
    try (
        -- 查找 HTTP 头和 JSON 体的分隔符（空行）
        local bodyStart = findString requestStr "\r\n\r\n"
        if bodyStart == undefined then throw "Invalid HTTP request: No body found"
        
        -- 提取 JSON 体
        local jsonStr = substring requestStr (bodyStart + 4) -1
        jsonStr
    ) catch (
        format "调试: HTTP 请求解析失败: %\n" (getCurrentException())
        undefined
    )
)

-- 解析嵌套 JSON 命令
fn parseJsonCommand jsonStr = (
    try (
        -- 解析 JSON 字符串（假设格式为 {"type":"executeCode","data":{"lang":"...","code":"..."}})
        local typeStart = findString jsonStr "\"type\":\"executeCode\""
        if typeStart == undefined then throw "Invalid JSON: 'type' must be 'executeCode'"
        
        local langStart = findString jsonStr "\"lang\":\"" + 8
        if langStart == undefined then throw "Invalid JSON: 'lang' field not found"
        
        local langEnd = findString jsonStr "\"" langStart
        if langEnd == undefined then throw "Invalid JSON: 'lang' closing quote not found"
        
        local lang = substring jsonStr langStart (langEnd - langStart)
        
        local codeStart = findString jsonStr "\"code\":\"" + 8
        if codeStart == undefined then throw "Invalid JSON: 'code' field not found"
        
        local codeEnd = findString jsonStr "\"}" -1
        if codeEnd == undefined then throw "Invalid JSON: 'code' closing '}' not found"
        
        local code = substring jsonStr codeStart (codeEnd - codeStart + 1)
        code = substituteString code "\\\"" "\"" -- 清理转义字符
        code = substituteString code "\\n" "\n" -- 处理换行符
        
        #(lang, code)
    ) catch (
        format "调试: JSON 解析失败: %\n" (getCurrentException())
        undefined
    )
)

-- 创建服务器结构
struct TCPServer (
    -- 公共属性
    port = 8123,
    
    -- 成员
    tcpListener = undefined,
    clientSocket = undefined,
    
    -- 启动服务器
    fn start = (
        try (
            -- 创建监听器
            local ipAddress = (dotNetClass "System.Net.IPAddress").Parse "127.0.0.1"
            format "调试: 解析 IP 地址: %\n" ipAddress
            tcpListener = dotNetObject "System.Net.Sockets.TcpListener" ipAddress port
            format "调试: 创建 tcpListener = %\n" tcpListener
            
            -- 尝试启动监听
            try (
                tcpListener.Start()
                format "服务器已启动在端口 %\n" port
                messageBox ("MaxMCP服务器已启动!\n监听地址: 127.0.0.1:" + port as string) title:"服务器启动"
                true
            ) catch (
                local errorMsg = "端口 % 可能被占用或无权限: %\n" port (getCurrentException())
                format "%\n" errorMsg
                messageBox errorMsg title:"错误"
                tcpListener = undefined
                false
            )
        ) catch (
            local errorMsg = "启动服务器失败: " + (getCurrentException())
            format "%\n" errorMsg
            messageBox errorMsg title:"错误"
            tcpListener = undefined
            false
        )
    ),
    
    -- 停止服务器
    fn stop = (
        if tcpListener != undefined do (
            try (
                tcpListener.Stop()
                format "服务器已停止\n"
            ) catch (
                format "停止监听器时出错: %\n" (getCurrentException())
            )
            tcpListener = undefined
        )
        if clientSocket != undefined do (
            try (
                clientSocket.Close()
            ) catch ()
            clientSocket = undefined
        )
    ),
    
    -- 处理客户端请求
    fn handleClient = (
        try (
            -- 检查是否有待处理的客户端连接（非阻塞）
            if tcpListener != undefined and tcpListener.Pending() then (
                format "调试: 检测到客户端连接...\n"
                clientSocket = tcpListener.AcceptSocket()
                format "调试: 客户端已连接: %\n" clientSocket
                
                -- 设置接收超时
                clientSocket.ReceiveTimeout = 5000 -- 5秒超时
                
                -- 接收数据
                local buffer = dotNetObject "System.Byte[]" 8192 -- 增加缓冲区大小
                local bytesRead = clientSocket.Receive buffer
                
                if bytesRead > 0 then (
                    -- 转换为字符串
                    local encoding = dotNetClass "System.Text.UTF8Encoding"
                    local command = trimright (encoding.UTF8.GetString buffer 0 bytesRead)
                    format "调试: 收到命令: %\n" command
                    
                    -- 解析 HTTP 请求
                    local jsonStr = parseHttpRequest command
                    if jsonStr == undefined then (
                        local errorMsg = "Error: Invalid HTTP request format"
                        format "调试: HTTP 请求解析失败: %\n" errorMsg
                        local response = "{\"status\":\"error\",\"message\":\"" + errorMsg + "\"}\n"
                        local responseBytes = encoding.UTF8.GetBytes response
                        clientSocket.Send responseBytes
                        format "调试: 已发送响应: %\n" response
                    ) else (
                        -- 解析 JSON 命令
                        local commandData = parseJsonCommand jsonStr
                        if commandData == undefined then (
                            local errorMsg = "Error: Invalid JSON command format"
                            format "调试: 命令解析失败: %\n" errorMsg
                            local response = "{\"status\":\"error\",\"message\":\"" + errorMsg + "\"}\n"
                            local responseBytes = encoding.UTF8.GetBytes response
                            clientSocket.Send responseBytes
                            format "调试: 已发送响应: %\n" response
                        ) else (
                            local lang = commandData[1]
                            local mxsCode = commandData[2]
                            
                            -- 执行代码
                            local result = undefined
                            try (
                                if lang == "mxs" then (
                                    -- 包裹代码以确保语法正确
                                    local wrappedCode = "(\n" + mxsCode + "\n)"
                                    result = execute wrappedCode
                                    format "调试: 命令执行成功: %\n" (result as string)
                                ) else if lang == "py" then (
                                    -- 执行 Python 代码
                                    python.execute mxsCode
                                    result = "Python code executed"
                                    format "调试: Python 命令执行成功: %\n" result
                                ) else (
                                    throw "Unsupported language: " + lang
                                )
                                local response = "{\"status\":\"success\",\"result\":\"" + (result as string) + "\"}\n"
                                local responseBytes = encoding.UTF8.GetBytes response
                                clientSocket.Send responseBytes
                                format "调试: 已发送响应: %\n" response
                            ) catch (
                                local errorMsg = "Error: " + (getCurrentException())
                                format "调试: 命令执行失败: %\n" errorMsg
                                local response = "{\"status\":\"error\",\"message\":\"" + errorMsg + "\"}\n"
                                local responseBytes = encoding.UTF8.GetBytes response
                                clientSocket.Send responseBytes
                                format "调试: 已发送响应: %\n" response
                            )
                        )
                    )
                ) else (
                    format "调试: 未接收到数据\n"
                    local response = "{\"status\":\"error\",\"message\":\"No data received\"}\n"
                    local responseBytes = encoding.UTF8.GetBytes response
                    clientSocket.Send responseBytes
                    format "调试: 已发送响应: %\n" response
                )
                
                -- 关闭连接
                clientSocket.Close()
                clientSocket = undefined
                format "调试: 客户端连接已关闭\n"
            )
        ) catch (
            format "调试: 处理客户端时出错: %\n" (getCurrentException())
            if clientSocket != undefined do (
                try (
                    clientSocket.Close()
                ) catch ()
                clientSocket = undefined
            )
        )
    ),
    
    -- 主循环（由定时器调用）
    fn run = (
        if tcpListener != undefined do (
            handleClient()
        )
    )
)

-- 停止定时器函数
fn stopServerTimer = (
    global serverTimer
    if serverTimer != undefined do (
        try (
            serverTimer.Stop()
            serverTimer = undefined
            format "调试: 定时器已停止\n"
        ) catch (
            format "调试: 停止定时器出错: %\n" (getCurrentException())
        )
    )
)

-- 创建并启动服务器
global gTCPServer = TCPServer()
if gTCPServer.start() then (
    -- 创建简单 UI 控制服务器
    try (destroyDialog serverControlRollout) catch ()
    rollout serverControlRollout "MaxMCP Server Control" width:200 height:150 (
        button btnStop "Stop Server" pos:[10,10] width:180 height:30
        button btnRestart "Restart Server" pos:[10,50] width:180 height:30
        label lblStatus "Server Running on port 8123" pos:[10,90] width:180 height:20
        
        on btnStop pressed do (
            if gTCPServer != undefined do (
                gTCPServer.stop()
                stopServerTimer()
                lblStatus.text = "Server Stopped"
                messageBox "服务器已停止" title:"服务器控制"
            )
        )
        
        on btnRestart pressed do (
            if gTCPServer != undefined do (
                gTCPServer.stop()
                stopServerTimer()
                gTCPServer = TCPServer()
                if gTCPServer.start() then (
                    global serverTimer
                    serverTimer = dotNetObject "System.Windows.Forms.Timer"
                    serverTimer.Interval = 200
                    fn serverTick = (
                        if gTCPServer.tcpListener != undefined then (
                            gTCPServer.run()
                            windows.processPostedMessages()
                            redrawViews()
                        ) else (
                            stopServerTimer()
                            serverControlRollout.lblStatus.text = "Server Stopped"
                        )
                    )
                    dotNet.addEventHandler serverTimer "Tick" serverTick
                    serverTimer.Start()
                    lblStatus.text = "Server Running on port 8123"
                    messageBox "服务器已重启" title:"服务器控制"
                ) else (
                    lblStatus.text = "Server Failed to Start"
                    messageBox "服务器重启失败" title:"错误"
                )
            )
        )
    )
    
    -- 使用定时器异步运行服务器主循环
    global serverTimer
    serverTimer = dotNetObject "System.Windows.Forms.Timer"
    serverTimer.Interval = 200 -- 200ms 平衡响应性和性能
    fn serverTick = (
        if gTCPServer.tcpListener != undefined then (
            gTCPServer.run()
            windows.processPostedMessages() -- 强制处理界面消息
            redrawViews() -- 强制刷新视图
        ) else (
            stopServerTimer()
            if serverControlRollout != undefined do (
                serverControlRollout.lblStatus.text = "Server Stopped"
            )
        )
    )
    dotNet.addEventHandler serverTimer "Tick" serverTick
    serverTimer.Start()
    format "调试: 服务器主循环已通过定时器启动\n"
    
    -- 显示控制面板
    createDialog serverControlRollout
) else (
    format "调试: 服务器启动失败，请检查端口或错误日志\n"
    messageBox "服务器启动失败，请检查端口或错误日志" title:"错误"
)