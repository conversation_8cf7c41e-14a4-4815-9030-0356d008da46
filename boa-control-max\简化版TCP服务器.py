# 3ds Max BOA Control Max 简化版TCP服务器
# 无需额外依赖模块版本
# 在3ds Max的Python脚本编辑器中运行此脚本

import json
import socket
import time
import os
import sys
import threading
from pymxs import runtime as rt

# 简化版TCP服务器类，不依赖于qtpy模块
class SimpleMaxTCPServer:
    def __init__(self, host='localhost', port=8123):
        self.host = host
        self.port = port
        self.running = False
        self.socket = None
        self.client = None
        self.buffer = b''
        self.thread = None
        
    def start(self):
        if self.running:
            print("服务器已在运行，正在重新启动...")
            self.stop()
            
        self.running = True
        self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        self.socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        
        try:
            self.socket.bind((self.host, self.port))
            self.socket.listen(1)
            
            # 使用线程而不是QTimer
            self.thread = threading.Thread(target=self._process_server)
            self.thread.daemon = True  # 守护线程，主线程退出时自动结束
            self.thread.start()
            
            print(f"MaxMCP简化版服务器已启动，监听地址: {self.host}:{self.port}")
            rt.messageBox(f"MaxMCP简化版服务器已启动!\n监听地址: {self.host}:{self.port}\n\n此版本不需要额外模块依赖。", 
                          title="BOA Control Max")
            return True
        except Exception as e:
            error_msg = f"服务器启动失败: {str(e)}"
            print(error_msg)
            rt.messageBox(error_msg, title="错误")
            self.stop()
            return False
            
    def stop(self):
        self.running = False
        
        if self.socket:
            self.socket.close()
        if self.client:
            self.client.close()
        
        self.socket = None
        self.client = None
        print("MaxMCP服务器已停止")

    def _process_server(self):
        """服务器处理函数，在独立线程中运行"""
        print("服务器线程已启动")
        
        while self.running:
            try:
                # 接受新连接
                if not self.client and self.socket:
                    try:
                        self.socket.settimeout(1)  # 设置超时，以便可以检查running标志
                        self.client, address = self.socket.accept()
                        self.client.settimeout(1)  # 同样设置客户端超时
                        print(f"客户端已连接: {address}")
                    except socket.timeout:
                        # 超时，继续循环检查running标志
                        continue
                    except Exception as e:
                        print(f"接受连接出错: {str(e)}")
                
                # 处理现有连接
                if self.client:
                    try:
                        # 尝试接收数据
                        try:
                            data = self.client.recv(8192)
                            if data:
                                self.buffer += data
                                # 尝试处理完整消息
                                try:
                                    # 尝试解析JSON
                                    command = json.loads(self.buffer.decode('utf-8'))
                                    print(f"收到命令: {command}")
                                    # 成功后，清除缓冲区并处理命令
                                    self.buffer = b''
                                    response = self.execute_command(command)
                                    response_json = json.dumps(response)
                                    self.client.sendall(response_json.encode('utf-8'))
                                except json.JSONDecodeError:
                                    # 数据不完整，保留在缓冲区
                                    pass
                            else:
                                # 客户端关闭连接
                                print("客户端断开连接")
                                self.client.close()
                                self.client = None
                                self.buffer = b''
                        except socket.timeout:
                            # 超时，继续循环
                            continue
                        except Exception as e:
                            print(f"接收数据出错: {str(e)}")
                            if self.client:
                                self.client.close()
                                self.client = None
                            self.buffer = b''
                    
                    except Exception as e:
                        print(f"客户端错误: {str(e)}")
                        if self.client:
                            self.client.close()
                            self.client = None
                        self.buffer = b''
                
                # 简单的线程暂停，避免CPU占用过高
                time.sleep(0.1)  
                
            except Exception as e:
                print(f"服务器错误: {str(e)}")
                time.sleep(1)  # 错误后暂停1秒
                
        print("服务器线程已退出")

    def execute_command(self, command):
        """执行从客户端接收的命令"""
        try:
            if isinstance(command, dict):
                if command.get('lang') == 'mxs':
                    return self.execute_mxs_code(command.get('code', ''))
                elif command.get('lang') == 'py':
                    return self.execute_py_code(command.get('code', ''))
                else:
                    return {"error": "无效的命令格式，缺少lang字段或值不正确"}
            else:
                return {"error": "命令必须是JSON对象"}
        except Exception as e:
            return {"error": str(e)}

    def execute_mxs_code(self, code):
        """执行任意3ds Max MaxScript代码"""
        try:
            result = rt.execute(code)
            return {"executed": True, "result": str(result)}
        except Exception as e:
            raise Exception(f"MaxScript执行错误: {str(e)}")

    def execute_py_code(self, code):
        """执行任意3ds Max Python代码"""
        try:
            print("执行Python代码...")
            print(code)
            # 创建一个本地命名空间，包含MaxScript运行时对象
            namespace = {"rt": rt}
            exec(code, namespace)
            return {"executed": True}
        except Exception as e:
            raise Exception(f"Python执行错误: {str(e)}")

# 启动服务器
if __name__ == "__main__":
    # 检查是否已经有服务器实例
    if 'server' in globals():
        print("停止现有服务器...")
        try:
            globals()['server'].stop()
        except:
            pass
    
    # 创建并启动新服务器
    server = SimpleMaxTCPServer()
    server.start() 