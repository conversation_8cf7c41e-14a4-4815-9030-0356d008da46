import json
import socket
import time
# 移除对 requests 模块的依赖，3ds Max环境中可能没有
# import requests 
import os
import threading
import sys
from pymxs import runtime as rt
import queue

# 使用3ds Max内置的PySide2而不是qtpy
from PySide2.QtCore import QObject, Signal, QThread, QMutex, QWaitCondition, QTimer
from PySide2.QtWidgets import QApplication

# Create a mechanism to execute code on the main thread
class MainThreadExecutor(QObject):
    """Helper class to execute code on the main thread using Qt signals and slots"""
    execute_signal = Signal(object)
    
    def __init__(self):
        super().__init__()
        # Move this object to the main thread
        self.moveToThread(QApplication.instance().thread())
        # Connect the signal to itself (slot is automatically created for the function)
        self.execute_signal.connect(self._execute)
    
    def _execute(self, func):
        """Execute the function on the main thread"""
        func()
        
    def execute(self, func):
        """Queue a function for execution on the main thread"""
        self.execute_signal.emit(func)

# Create a singleton instance
executor = MainThreadExecutor()

class MaxCommandExecutor(QObject):
    """在主线程中执行Max命令的类"""
    execute_signal = Signal(object, object)  # (command, result_queue)
    
    def __init__(self):
        super().__init__()
        self.moveToThread(QApplication.instance().thread())
        self.execute_signal.connect(self._execute_command)
        
    def _execute_command(self, command, result_queue):
        """在主线程中执行命令"""
        try:
            if command['lang'] == 'mxs':
                result = rt.execute(command['code'])
                result_queue.put({"executed": True, "result": str(result)})
            elif command['lang'] == 'py':
                namespace = {"rt": rt}
                exec(command['code'], namespace)
                result_queue.put({"executed": True})
            else:
                result_queue.put({"error": "不支持的语言类型"})
        except Exception as e:
            result_queue.put({"error": f"执行命令时出错: {str(e)}"})

class MaxMCPServer:
    def __init__(self, host='localhost', port=8123):
        self.host = host
        self.port = port
        self.running = False
        self.socket = None
        self.command_queue = queue.Queue()
        self.response_queues = {}
        self.thread = None
        self.executor = MaxCommandExecutor()
        
    def start(self):
        """启动TCP服务器"""
        if self.running:
            print("服务器已在运行，正在重新启动...")
            self.stop()
            
        self.running = True
        self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        self.socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        
        try:
            self.socket.bind((self.host, self.port))
            self.socket.listen(1)
            
            # 启动接收线程
            self.thread = threading.Thread(target=self._accept_connections)
            self.thread.daemon = True
            self.thread.start()
            
            print(f"MaxMCP服务器已启动，监听地址: {self.host}:{self.port}")
            rt.messageBox(f"MaxMCP服务器已启动!\n监听地址: {self.host}:{self.port}")
            
            # 在主线程中处理命令
            while self.running:
                try:
                    # 非阻塞方式检查命令队列
                    try:
                        client_id, command = self.command_queue.get_nowait()
                        response = self._execute_command(command)
                        if client_id in self.response_queues:
                            self.response_queues[client_id].put(response)
                    except queue.Empty:
                        pass
                    
                    # 短暂休眠以避免CPU占用过高
                    time.sleep(0.1)
                except Exception as e:
                    print(f"处理命令时出错: {str(e)}")
            
            return True
            
        except Exception as e:
            error_msg = f"服务器启动失败: {str(e)}"
            print(error_msg)
            rt.messageBox(error_msg, title="错误")
            self.stop()
            return False
    
    def stop(self):
        """停止TCP服务器"""
        self.running = False
        if self.socket:
            try:
                self.socket.close()
            except:
                pass
        self.socket = None
        print("MaxMCP服务器已停止")
    
    def _accept_connections(self):
        """接受客户端连接的线程函数"""
        while self.running:
            try:
                self.socket.settimeout(1)
                client, address = self.socket.accept()
                print(f"客户端已连接: {address}")
                
                # 为每个客户端创建一个处理线程
                client_thread = threading.Thread(
                    target=self._handle_client,
                    args=(client, address)
                )
                client_thread.daemon = True
                client_thread.start()
                
            except socket.timeout:
                continue
            except Exception as e:
                if self.running:
                    print(f"接受连接时出错: {str(e)}")
                time.sleep(1)
    
    def _handle_client(self, client, address):
        """处理单个客户端连接的线程函数"""
        buffer = b""
        response_queue = queue.Queue()
        client_id = id(client)
        self.response_queues[client_id] = response_queue
        
        try:
            while self.running:
                try:
                    client.settimeout(1)
                    data = client.recv(8192)
                    if not data:
                        break
                        
                    buffer += data
                    try:
                        # 尝试解析JSON
                        command = json.loads(buffer.decode('utf-8'))
                        buffer = b""
                        
                        # 将命令放入队列，等待主线程处理
                        self.command_queue.put((client_id, command))
                        
                        # 等待响应
                        try:
                            response = response_queue.get(timeout=10)
                            response_json = json.dumps(response)
                            client.sendall(response_json.encode('utf-8'))
                        except queue.Empty:
                            error_response = {
                                "error": "命令执行超时"
                            }
                            client.sendall(json.dumps(error_response).encode('utf-8'))
                            
                    except json.JSONDecodeError:
                        # 数据不完整，继续等待
                        continue
                        
                except socket.timeout:
                    continue
                    
        except Exception as e:
            print(f"处理客户端 {address} 时出错: {str(e)}")
        finally:
            del self.response_queues[client_id]
            try:
                client.close()
            except:
                pass
            print(f"客户端 {address} 已断开连接")
    
    def _execute_command(self, command):
        """执行命令"""
        try:
            if not isinstance(command, dict):
                return {"error": "无效的命令格式"}
                
            if "lang" not in command:
                return {"error": "命令中缺少lang字段"}
                
            if command["lang"] == "mxs":
                if "code" not in command:
                    return {"error": "命令中缺少code字段"}
                result = rt.execute(command["code"])
                return {"executed": True, "result": str(result)}
                
            elif command["lang"] == "py":
                if "code" not in command:
                    return {"error": "命令中缺少code字段"}
                namespace = {"rt": rt}
                exec(command["code"], namespace)
                return {"executed": True}
                
            else:
                return {"error": f"不支持的语言类型: {command['lang']}"}
                
        except Exception as e:
            return {"error": f"执行命令时出错: {str(e)}"}

# 全局服务器实例
_server = None

def start_server():
    """启动全局服务器实例"""
    global _server
    if _server is not None:
        _server.stop()
    _server = MaxMCPServer()
    return _server.start()

def stop_server():
    """停止全局服务器实例"""
    global _server
    if _server is not None:
        _server.stop()
        _server = None

# 当直接运行时创建服务器实例
if __name__ == "__main__":
    start_server()    