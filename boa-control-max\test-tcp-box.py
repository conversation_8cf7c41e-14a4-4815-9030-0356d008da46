import socket
import json
import time

def send_tcp_command(host='localhost', port=8123):
    # 创建TCP连接
    client = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    try:
        print(f"连接到服务器 {host}:{port}...")
        client.connect((host, port))
        
        # 创建MaxScript命令 - 创建一个红色的box
        command = {
            "lang": "mxs",
            "code": 'box length:100 width:100 height:100 wirecolor:red name:"TCP_TEST_BOX"'
        }
        
        # 发送命令
        print("发送创建Box命令...")
        command_json = json.dumps(command)
        client.sendall(command_json.encode('utf-8'))
        
        # 等待响应
        print("等待响应...")
        response = b""
        while True:
            data = client.recv(8192)
            if data:
                response += data
                try:
                    # 尝试解析响应
                    result = json.loads(response.decode('utf-8'))
                    print(f"收到响应: {result}")
                    break
                except json.JSONDecodeError:
                    # 继续接收更多数据
                    continue
            else:
                print("连接已关闭")
                break
                
        return response.decode('utf-8')
    except Exception as e:
        print(f"错误: {str(e)}")
        return None
    finally:
        client.close()

if __name__ == "__main__":
    result = send_tcp_command()
    print("\n完成! 请检查3ds Max中是否创建了红色Box。")
    input("按Enter退出...") 