import socket
import json
import sys
import time

def send_max_command(code, lang="mxs", host='localhost', port=8123):
    """
    向3ds Max TCP服务器发送命令
    
    参数:
        code (str): 要执行的MaxScript或Python代码
        lang (str): 语言类型，'mxs'或'py'
        host (str): 服务器主机名
        port (int): 服务器端口号
    
    返回:
        dict: 服务器响应
    """
    # 创建TCP连接
    client = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    try:
        print(f"连接到服务器 {host}:{port}...")
        client.connect((host, port))
        
        # 创建命令
        command = {
            "lang": lang,
            "code": code
        }
        
        # 发送命令
        print(f"发送{lang}命令: {code}")
        command_json = json.dumps(command)
        client.sendall(command_json.encode('utf-8'))
        
        # 等待响应
        print("等待响应...")
        response = b""
        start_time = time.time()
        timeout = 10  # 10秒超时
        
        while time.time() - start_time < timeout:
            data = client.recv(8192)
            if data:
                response += data
                try:
                    # 尝试解析响应
                    result = json.loads(response.decode('utf-8'))
                    print(f"收到响应: {result}")
                    return result
                except json.JSONDecodeError:
                    # 继续接收更多数据
                    continue
            else:
                print("连接已关闭")
                break
                
        print("响应超时")
        return {"error": "响应超时"}
    except Exception as e:
        print(f"错误: {str(e)}")
        return {"error": str(e)}
    finally:
        client.close()

if __name__ == "__main__":
    # 从命令行参数获取代码
    if len(sys.argv) < 2:
        print("用法: python max-command.py \"MaxScript代码\"")
        print("例如: python max-command.py \"box length:100 width:100 height:100\"")
        sys.exit(1)
        
    # 默认使用MaxScript
    code = sys.argv[1]
    lang = "mxs"
    
    # 可选的第二个参数指定语言
    if len(sys.argv) > 2:
        lang = sys.argv[2]
        
    # 发送命令
    result = send_max_command(code, lang)
    
    # 打印结果
    if isinstance(result, dict) and "executed" in result and result["executed"]:
        print("\n命令执行成功!")
        if "result" in result:
            print(f"结果: {result['result']}")
    else:
        print("\n命令执行失败!")
        print(f"错误: {result}")
    
    input("\n按Enter键退出...") 