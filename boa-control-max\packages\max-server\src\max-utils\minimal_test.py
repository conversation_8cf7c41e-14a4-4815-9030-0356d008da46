import socket

print("开始最小化测试...")

# 创建socket连接
sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
sock.settimeout(10)

try:
    # 连接到服务器
    print("正在连接...")
    sock.connect(('localhost', 8123))
    print("已连接")
    
    # 发送简单命令
    command = 'sphere radius:25 wirecolor:green name:"TEST_SPHERE"'
    print(f"发送命令: {command}")
    sock.sendall(command.encode('utf-8'))
    
    # 等待响应
    print("等待响应...")
    response = sock.recv(8192).decode('utf-8')
    print(f"收到响应: {response}")
    
except Exception as e:
    print(f"错误: {str(e)}")
finally:
    sock.close()
    
input("按Enter键退出...") 