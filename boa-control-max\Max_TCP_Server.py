# 3ds Max BOA Control Max TCP服务器启动脚本
# 在3ds Max的Python脚本编辑器中运行此脚本

import os
import sys
from pymxs import runtime as rt

# 获取 3ds Max 的 Python 模块路径
MAX_PYTHON_PATH = os.path.dirname(sys.executable) + "\\Python\\Lib\\site-packages"
print(f"3ds Max Python 路径: {MAX_PYTHON_PATH}")

# 检查是否已经加载了服务器脚本
def check_server_status():
    if 'server' in globals() and hasattr(globals()['server'], 'running') and globals()['server'].running:
        print("服务器已在运行中")
        return True
    return False

# 服务器脚本路径
SERVER_SCRIPT_PATH = r"D:\AI\MCP_MAX\boa-control-max\packages\max-server\src\max-utils\maxPyTcpServer.py"

def start_max_server():
    """启动 3ds Max TCP 服务器"""
    # 检查服务器状态
    if check_server_status():
        choice = rt.queryBox("服务器已在运行中。是否要重启？", title="BOA Control Max")
        if choice:
            try:
                globals()['server'].stop()
                print("已停止现有服务器")
            except:
                print("无法停止现有服务器")
        else:
            print("继续使用现有服务器")
            return
    
    # 执行服务器脚本
    try:
        if os.path.exists(SERVER_SCRIPT_PATH):
            print(f"正在加载服务器脚本: {SERVER_SCRIPT_PATH}")
            
            # 尝试执行脚本 - 使用 UTF-8 编码读取文件
            try:
                # 明确指定使用 UTF-8 编码读取文件，解决中文编码问题
                with open(SERVER_SCRIPT_PATH, 'r', encoding='utf-8') as f:
                    script_content = f.read()
                
                exec(script_content, globals())
                print("TCP服务器脚本已成功执行!")
                
                # 显示成功对话框
                rt.messageBox("TCP服务器已成功启动!\n现在可以使用 HTTP 服务器连接到 3ds Max。", 
                              title="BOA Control Max")
            except Exception as e:
                error_msg = f"执行脚本时出错: {str(e)}"
                print(error_msg)
                rt.messageBox(error_msg, title="错误")
        else:
            error_msg = f"找不到服务器脚本: {SERVER_SCRIPT_PATH}"
            print(error_msg)
            rt.messageBox(error_msg, title="错误")
    except Exception as e:
        error_msg = f"启动服务器时发生错误: {str(e)}"
        print(error_msg)
        rt.messageBox(error_msg, title="错误")

# 运行服务器
if __name__ == "__main__":
    start_max_server() 