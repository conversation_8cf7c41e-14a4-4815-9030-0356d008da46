-- Reset the scene
resetMaxFile #noPrompt

-- Create Sun
sun = Sphere radius:20 segments:32
sun.name = "Sun"
sun.position = [0,0,0]
sun_mat = StandardMaterial()
sun_mat.diffuse = (color 255 165 0)  -- orange
sun_mat.selfIllumAmount = 100
sun.material = sun_mat

-- Create Earth
earth = Sphere radius:8 segments:32
earth.name = "Earth"
earth.position = [100,0,0]
earth_mat = StandardMaterial()
earth_mat.diffuse = (color 0 0 255)  -- blue
earth.material = earth_mat

-- Create Moon
moon = Sphere radius:3 segments:32
moon.name = "Moon"
moon.position = [130,0,0]
moon_mat = StandardMaterial()
moon_mat.diffuse = (color 128 128 128)  -- gray
moon.material = moon_mat

-- Set animation range
animationRange = interval 0 300

-- Create animation
for t = 0 to 300 do
(
    with animate on
    (
        at time t
        (
            -- Earth orbit
            earth_angle = (t as float / 300.0) * 360
            earth_x = 100 * cos(earth_angle)
            earth_z = 100 * sin(earth_angle)
            earth.position = [earth_x, 0, earth_z]
            
            -- Moon orbit (3x faster than Earth)
            moon_angle = earth_angle * 3
            moon_offset_x = 30 * cos(moon_angle)
            moon_offset_z = 30 * sin(moon_angle)
            moon.position = [earth_x + moon_offset_x, 0, earth_z + moon_offset_z]
        )
    )
)

-- Add camera
cam = FreeCamera()
cam.position = [0,-300,200]
if cam.target != undefined do cam.target.position = [0,0,0]

-- Add light
sun_light = Omnilight()
sun_light.position = [0,0,0]
sun_light.multiplier = 2

-- Set viewport to use the new camera
viewport.setCamera cam
