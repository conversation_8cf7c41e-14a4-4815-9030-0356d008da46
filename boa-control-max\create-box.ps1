# 3ds Max 创建Box测试脚本
Write-Host "正在向3ds Max发送创建Box的命令..." -ForegroundColor Cyan

# 构建JSON请求体
$body = @{
    type = "executeCode"
    lang = "mxs"
    data = @{
        code = "box length:100 width:100 height:100 wirecolor:green name:""PowerShell_Created_Box"""
    }
} | ConvertTo-Json

# 发送API请求
try {
    Write-Host "发送请求到: http://localhost:8123/api/request/createRequest" -ForegroundColor Yellow
    
    $response = Invoke-RestMethod -Uri "http://localhost:8123/api/request/createRequest" `
        -Method Post `
        -ContentType "application/json" `
        -Body $body
    
    Write-Host "请求成功!" -ForegroundColor Green
    Write-Host "请求ID: $($response.requestId)" -ForegroundColor Green
    Write-Host "请检查3ds Max中是否创建了绿色Box" -ForegroundColor Green
}
catch {
    Write-Host "错误: $_" -ForegroundColor Red
    Write-Host "请确保:" -ForegroundColor Yellow
    Write-Host "1. HTTP服务器运行在端口8123" -ForegroundColor Yellow
    Write-Host "2. 3ds Max正在运行且TCP服务器已启动" -ForegroundColor Yellow
}

Write-Host "`n按任意键退出..." -ForegroundColor Cyan
$null = $host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown") 