{"compilerOptions": {"target": "ES2020", "module": "ESNext", "lib": ["ES2020", "DOM"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "bundler", "resolveJsonModule": true, "declaration": true, "allowJs": true, "noImplicitAny": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}