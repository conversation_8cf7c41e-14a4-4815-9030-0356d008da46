-- BOA Control Max TCP 服务器启动脚本
-- 在3ds Max中运行此脚本
-- 使用无模块依赖的简化版服务器

fn startBoaServerInMax = 
(
    -- 定义 Python 启动脚本的路径（使用无依赖版本）
    pythonScriptPath = "D:\\AI\\MCP_MAX\\boa-control-max\\简化版TCP服务器.py"
    
    if doesFileExist pythonScriptPath then
    (
        print "找到Python启动脚本，正在加载..."
        try
        (
            python.ExecuteFile pythonScriptPath
            print "成功执行Python启动脚本!"
        )
        catch
        (
            errorMessage = getCurrentException()
            messageBox ("执行Python脚本时出错: " + errorMessage) title:"错误"
            print ("错误: " + errorMessage)
        )
    )
    else
    (
        messageBox ("找不到Python脚本: " + pythonScriptPath + "\n请确认路径正确。") title:"文件未找到"
        print ("错误: 找不到文件 " + pythonScriptPath)
    )
)

-- 执行启动函数
startBoaServerInMax() 