import socket
import json

print("开始测试...")

# 创建socket连接
sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)

try:
    # 连接到服务器
    print("正在连接到服务器...")
    sock.connect(('localhost', 8123))
    print("连接成功！")
    
    # 创建测试命令
    command = {
        "lang": "mxs",
        "code": "box length:50 width:50 height:50 wirecolor:red name:\"TEST_BOX\""
    }
    
    # 发送命令
    print("发送命令：创建红色方块...")
    sock.sendall(json.dumps(command).encode('utf-8'))
    
    # 接收响应
    print("等待响应...")
    response = sock.recv(8192).decode('utf-8')
    print("服务器响应:", response)
    
except Exception as e:
    print("错误:", str(e))
finally:
    sock.close()
    
input("按Enter键退出...") 