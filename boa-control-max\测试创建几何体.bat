@echo off
echo BOA Control Max - TCP通信测试工具
echo ===============================
echo.

REM 检查Python是否可用
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] 没有找到Python，请确保安装了Python并添加到PATH中
    goto :end
)

echo [提示] 确保3ds Max已启动并运行了TCP服务器
echo [提示] 服务器应该监听在 localhost:8123
echo.
echo 请选择要执行的测试:
echo 1. 创建红色盒子
echo 2. 创建蓝色盒子
echo 3. 创建黄色球体 
echo 4. 创建紫色圆柱
echo 5. 缩放视图以显示所有对象
echo 6. 清空场景
echo 7. 执行自定义MaxScript命令
echo 8. 退出
echo.

:menu
set /p option=请输入选项 (1-8): 

if "%option%"=="1" (
    echo 正在创建红色盒子...
    python max-command.py "box length:100 width:100 height:100 wirecolor:red name:\"TCP_RED_BOX\""
    goto :menu
)

if "%option%"=="2" (
    echo 正在创建蓝色盒子...
    python max-command.py "box length:80 width:80 height:150 wirecolor:blue name:\"TCP_BLUE_BOX\""
    goto :menu
)

if "%option%"=="3" (
    echo 正在创建黄色球体...
    python max-command.py "sphere radius:50 wirecolor:yellow name:\"TCP_YELLOW_SPHERE\""
    goto :menu
)

if "%option%"=="4" (
    echo 正在创建紫色圆柱...
    python max-command.py "cylinder radius:40 height:120 wirecolor:purple name:\"TCP_PURPLE_CYLINDER\""
    goto :menu
)

if "%option%"=="5" (
    echo 正在缩放视图...
    python max-command.py "max zoomextents all"
    goto :menu
)

if "%option%"=="6" (
    echo 正在清空场景...
    python max-command.py "delete objects"
    goto :menu
)

if "%option%"=="7" (
    echo 执行自定义MaxScript命令
    set /p cmd=请输入MaxScript命令: 
    python max-command.py "%cmd%"
    goto :menu
)

if "%option%"=="8" (
    goto :end
) else (
    echo 无效选项，请重新输入
    goto :menu
)

:end
echo.
echo 测试结束，感谢使用！
pause 