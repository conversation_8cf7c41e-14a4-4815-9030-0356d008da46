# BOA Control Max

BOA Control Max 是一个用于在3ds Max中执行MaxScript和Python代码的TCP通信工具。它提供了一个简单的方式来从外部应用程序控制3ds Max。

## 特点

- 纯Python实现，无需额外依赖
- 支持MaxScript和Python代码执行
- 线程安全的命令执行
- 简单易用的客户端API
- 完整的错误处理和超时机制

## 项目结构

```
boa-control-max/
├── server/                # 服务器端代码
│   ├── max_tcp_server.py # TCP服务器实现
│   └── start_server.ms   # 服务器启动脚本
├── client/               # 客户端代码
│   ├── max_client.py    # 客户端库
│   └── test_client.py   # 测试脚本
└── README.md            # 项目说明文档
```

## 快速开始

### 1. 启动服务器

1. 打开3ds Max
2. 在MAXScript编辑器中加载并运行 `server/start_server.ms`
3. 确认看到"MaxMCP服务器已启动"的消息

### 2. 运行测试

执行测试脚本：
```bash
cd client
python test_client.py
```

这将在3ds Max中创建一些测试对象。

## 使用方法

### Python客户端示例

```python
from max_client import MaxClient

# 创建客户端实例
client = MaxClient()

# 连接到服务器
client.connect()

# 创建一个红色盒子
client.create_box(
    length=100,
    width=100,
    height=100,
    wirecolor="red",
    name="MY_BOX"
)

# 创建一个黄色球体
client.create_sphere(
    radius=50,
    wirecolor="yellow",
    name="MY_SPHERE"
)

# 缩放视图以显示所有对象
client.zoom_extents()

# 关闭连接
client.close()
```

### 直接发送命令

您也可以直接发送MaxScript或Python命令：

```python
# 发送MaxScript命令
client.send_command('box length:100 width:100 height:100', lang="mxs")

# 发送Python命令
client.send_command('rt.sphere(radius=50)', lang="py")
```

## 常见问题

### 1. 连接错误

确保：
- 3ds Max已启动
- 服务器脚本已在Max中运行
- 端口8123未被其他程序占用

### 2. 命令执行错误

- 检查MaxScript或Python代码语法
- 查看Max的MAXScript监听器窗口中的错误信息
- 确保命令在Max支持的范围内

### 3. 线程相关错误

服务器已经处理了线程安全问题，确保所有MaxScript命令都在Max的主线程中执行。如果仍然遇到问题，请检查：
- Max的脚本安全设置
- 是否有其他脚本正在运行
- Max的系统负载状态

## 注意事项

1. 服务器默认监听localhost:8123
2. 所有命令都有10秒的超时限制
3. 建议在发送新命令前等待前一个命令的响应
4. 保持命令简单，避免发送过于复杂的操作

## 许可证

MIT License
