# BOA Control Max 快速部署指南

## 问题修复及部署步骤

我们已经修复了您遇到的三个问题：
1. `requests` 模块缺失问题
2. 中文编码问题 (`'gbk' codec can't decode byte 0xb9 in position 51: illegal multibyte sequence`)
3. `qtpy` 模块缺失问题

我们现在提供了一个无外部依赖的简化版本，可以直接在 3ds Max 中运行，无需安装任何额外模块。

请按照以下步骤操作：

### 1. 启动 HTTP 服务器

打开命令提示符或 PowerShell，执行：

```
cd D:\AI\MCP_MAX\boa-control-max
npx pnpm run dev
```

服务器应该会在 http://localhost:8123/ 上启动。

### 2. 在 3ds Max 中启动 TCP 服务器

我们提供了下面推荐的方法，使用无依赖版本的简化服务器：

#### 方法 A：使用简化版 MaxScript（最简单，无依赖）

1. 打开 3ds Max 2024
2. 点击主菜单中的 "MAXScript" > "Run Script..."
3. 浏览并选择 `D:\AI\MCP_MAX\boa-control-max\启动TCP服务器.ms` 文件
4. 点击 "Open" 运行脚本

这个脚本会自动调用简化版的 Python TCP 服务器，该服务器不需要任何额外的 Python 模块。

#### 方法 B：直接使用简化版 Python 服务器

1. 打开 3ds Max 2024
2. 点击主菜单中的 "Scripting" > "New Script"（或按 F11）
3. 确保选择的是 "Python" 模式
4. 将 `D:\AI\MCP_MAX\boa-control-max\简化版TCP服务器.py` 文件的内容粘贴到编辑器中，然后运行

或者，您可以直接在 Python 编辑器中执行：
```python
import os
script_path = r"D:\AI\MCP_MAX\boa-control-max\简化版TCP服务器.py"
if os.path.exists(script_path):
    with open(script_path, 'r', encoding='utf-8') as f:
        exec(f.read())
else:
    print(f"错误：找不到文件 {script_path}")
```

### 3. 验证连接

如果一切正常，您应该能在 3ds Max 的 MAXScript 监听器窗口中看到：
```
MaxMCP简化版服务器已启动，监听地址: localhost:7603
服务器线程已启动
```

同时会出现一个对话框，确认服务器已成功启动。

### 4. 测试 API

打开浏览器，访问 http://localhost:8123/ 查看服务器是否正常运行。

然后，您可以尝试一个简单的 API 测试：在浏览器控制台或任何支持 HTTP 请求的工具中执行：

```javascript
fetch("http://localhost:8123/api/request/createRequest", {
  method: "POST",
  headers: { "Content-Type": "application/json" },
  body: JSON.stringify({
    type: "executeCode",
    lang: "mxs",
    data: { code: "sphere radius:50 wirecolor:red" },
  }),
})
.then(response => response.json())
.then(data => console.log(data));
```

这应该会在 3ds Max 中创建一个红色的球体。

## 常见问题解决

### 1. 依赖模块问题

我们的简化版 TCP 服务器已移除了对外部模块的所有依赖，仅使用 Python 标准库和 3ds Max 内置的模块。您无需安装任何额外的 Python 模块。

### 2. 编码问题

我们已解决中文环境下的编码问题，所有文件读取操作均使用 UTF-8 编码。

### 3. 如果仍然无法启动

请检查：
- 文件路径是否正确
- 确保 3ds Max 和命令行都有足够的权限
- 检查 MAXScript 监听器中的错误消息

### 4. 网络和防火墙问题

- 确保端口 7603 和 8123 没有被其他应用程序占用
- 检查防火墙设置，确保允许这些端口的通信 

## 方法1：使用命令行工具

执行以下命令：

```
python boa-control-max/max-command.py "box length:100 width:100 height:100 wirecolor:green name:\"TEST_BOX\""
```

## 方法2：使用批处理文件

1. 双击运行 `boa-control-max/测试创建几何体.bat`
2. 在菜单中选择选项1（创建红色盒子）

## 方法3：使用测试脚本

直接运行测试脚本：

```
python boa-control-max/test-tcp-box.py
```

我已经创建了一套完整的测试工具，以下是它们的功能：

1. **test-tcp-box.py** - 直接创建一个红色盒子的简单测试
2. **max-command.py** - 通用命令行工具，可以发送任何MaxScript或Python代码
3. **测试创建几何体.bat** - 交互式菜单，提供多种测试选项
4. **tcp-test.html** - 网页界面演示（仅作演示用，浏览器无法直接连接TCP）
5. **测试工具使用说明.md** - 详细的使用说明文档

这些工具应该能够帮助您测试与3ds Max的TCP通信。请注意，原版的BOA Control Max可能同时支持HTTP和TCP服务器，但目前的简化版只实现了TCP服务器功能，这就是为什么HTTP请求会返回404错误。

如果遇到问题，请确保：
1. 3ds Max已启动
2. TCP服务器已启动并监听在8123端口（"MaxMCP简化版服务器已启动，监听地址：localhost:8123"）
3. 您的防火墙没有阻止这些连接 