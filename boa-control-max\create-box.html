<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>创建Box测试</title>
    <style>
        body { font-family: 'Microsoft YaHei', Arial, sans-serif; padding: 20px; max-width: 600px; margin: 0 auto; }
        button { padding: 10px 15px; background: #4CAF50; color: white; border: none; border-radius: 4px; cursor: pointer; }
        #result { margin-top: 20px; padding: 10px; border: 1px solid #ddd; white-space: pre-wrap; }
    </style>
</head>
<body>
    <h1>3ds Max - 创建Box测试</h1>
    <button onclick="createBox()">创建Box</button>
    <div id="result"></div>

    <script>
        // 直接创建Box的函数
        async function createBox() {
            try {
                document.getElementById('result').textContent = '正在发送请求...';
                
                const response = await fetch('http://localhost:8123/api/request/createRequest', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        type: 'executeCode',
                        lang: 'mxs',
                        data: { code: 'box length:100 width:100 height:100 wirecolor:blue' }
                    })
                });
                
                const data = await response.json();
                document.getElementById('result').textContent = JSON.stringify(data, null, 2);
                
                if(data.requestId) {
                    alert('请求已发送！请检查3ds Max中是否创建了蓝色Box');
                }
            } catch (error) {
                document.getElementById('result').textContent = `错误: ${error.message}`;
            }
        }

        // 页面加载时自动发送请求
        window.onload = createBox;
    </script>
</body>
</html> 