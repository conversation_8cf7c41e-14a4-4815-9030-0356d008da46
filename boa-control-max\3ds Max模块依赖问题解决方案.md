# 3ds Max Python 模块依赖问题解决方案

## 问题概述

在 3ds Max 中运行 Python 脚本时，常常会遇到以下模块缺失错误：

```
ModuleNotFoundError: No module named 'requests'
ModuleNotFoundError: No module named 'qtpy'
```

这是因为 3ds Max 内置的 Python 环境与标准 Python 环境不同，仅包含一部分常用模块。

## 解决方案

### 方案 1：使用我们提供的无依赖版本（推荐）

我们已经创建了一个简化版的 TCP 服务器实现，不依赖于任何外部模块，仅使用 Python 标准库和 3ds Max 内置模块。

请使用以下文件：
- `简化版TCP服务器.py` - 无依赖版本的 TCP 服务器
- `启动TCP服务器.ms` - 用于启动无依赖版本的 MaxScript 脚本

### 方案 2：在 3ds Max Python 环境中安装缺失的模块

如果您需要在 3ds Max 中使用其他 Python 模块，可以按照以下步骤安装：

1. **以管理员身份打开命令提示符**

2. **导航到 3ds Max Python 目录**：
   ```
   cd "C:\Program Files\Autodesk\3ds Max 2024\Python"
   ```

3. **使用 pip 安装模块**：
   ```
   .\python.exe -m pip install requests qtpy
   ```

4. **验证安装**：
   在 3ds Max 中打开 Python 编辑器，尝试导入模块：
   ```python
   import sys
   print(sys.path)
   
   try:
      import requests
      print("requests 模块已安装:", requests.__version__)
   except ImportError:
      print("requests 模块未安装")
      
   try:
      import qtpy
      print("qtpy 模块已安装:", qtpy.__version__)
   except ImportError:
      print("qtpy 模块未安装")
   ```

## 无依赖版本的优势

无依赖版本的 TCP 服务器具有以下优势：

1. **无需安装额外模块**：完全使用 Python 标准库实现
2. **更简单的部署**：无需额外配置步骤
3. **更好的兼容性**：适用于所有 3ds Max 版本
4. **更简单的维护**：无需担心模块版本和依赖关系

## 详细说明

### requests 模块

`requests` 模块用于发送 HTTP 请求。在原始实现中，此模块用于与 HTTP 服务器通信，但在我们的无依赖版本中已被移除，因为核心功能不依赖于此。

### qtpy 模块

`qtpy` 模块是一个 Qt 绑定的抽象层，在原始实现中用于处理事件循环和定时器。在我们的无依赖版本中，我们使用了标准的 Python `threading` 模块来替代。

## 注意事项

1. 如果安装了额外模块后，3ds Max 启动时出现问题，可能是模块兼容性问题。此时请使用我们的无依赖版本。

2. 在某些情况下，管理员权限可能不足以在 Program Files 目录中安装 Python 模块。此时，请考虑使用虚拟环境或使用我们的无依赖版本。

3. 不同版本的 3ds Max 可能包含不同版本的 Python，请确保安装与 3ds Max Python 版本兼容的模块。 