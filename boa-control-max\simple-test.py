import socket
import json

# 创建TCP连接
client = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
print("正在连接到服务器...")

try:
    # 连接到服务器
    client.connect(('localhost', 8123))
    print("连接成功")
    
    # 创建最简单的命令
    command = {
        "lang": "mxs",
        "code": "box()"
    }
    
    # 发送命令
    print("发送命令: box()")
    command_json = json.dumps(command)
    client.sendall(command_json.encode('utf-8'))
    
    # 等待响应
    print("等待响应...")
    response = client.recv(8192)
    if response:
        print(f"收到响应: {response.decode('utf-8')}")
    
except Exception as e:
    print(f"错误: {str(e)}")
finally:
    client.close()
    
input("按Enter键退出...") 