# BOA Control Max 安装指南

这个指南将帮助您在 3ds Max 2024 环境中设置 BOA Control Max。

## 前提条件

- 已安装 Node.js 和 npm
- 已安装 3ds Max 2024 (路径: `C:\Program Files\Autodesk\3ds Max 2024`)
- 已克隆 BOA Control Max 仓库

## 步骤 1: 启动 HTTP 服务器

1. 打开命令提示符
2. 导航到 BOA Control Max 目录:
   ```
   cd D:\AI\MCP_MAX\boa-control-max
   ```
3. 安装 pnpm (如果尚未安装):
   ```
   npm install -g pnpm
   ```
4. 安装项目依赖:
   ```
   pnpm install
   ```
5. 启动服务器:
   ```
   pnpm run dev
   ```
6. 服务器应该会在 http://localhost:8123/ 上启动

## 步骤 2: 在 3ds Max 中启动 TCP 服务器

### 方法 1: 使用 Python 脚本 (推荐)

1. 打开 3ds Max 2024
2. 点击主菜单中的 "脚本编辑器" 或使用快捷键 (通常是 F11)
3. 确保选择的是 "Python" 模式
4. 在脚本编辑器中执行以下代码:

```python
# 修改为您电脑上的实际路径
script_path = r"D:\AI\MCP_MAX\boa-control-max\start_max_server.py"
exec(open(script_path).read())
```

### 方法 2: 使用 MaxScript

1. 打开 3ds Max 2024 
2. 点击主菜单中的 "MAXScript" > "Run Script..."
3. 浏览并选择 `D:\AI\MCP_MAX\boa-control-max\start_max_server.ms` 文件
4. 点击 "Open" 运行脚本

## 步骤 3: 验证连接

如果一切正常，您应该能在 3ds Max 的 MAXScript 监听器窗口中看到:
```
正在启动 3ds Max TCP 服务器...
MaxMCP server started on localhost:7603
TCP 服务器脚本已执行
```

## 故障排除

如果遇到问题:

1. **路径问题**: 确保脚本中的路径正确指向您克隆的仓库位置
2. **端口冲突**: 如果端口 7603 已被占用，您可以修改 `maxPyTcpServer.py` 文件中的端口号
3. **权限问题**: 确保您有足够的权限运行这些脚本

## 使用 MCP 与 AI 工具集成

如果您使用 Cursor 编辑器:

1. 确保 Cursor 的 MCP 功能已启用
2. BOA Control Max 已包含 `.cursor/mcp.json` 配置文件

## API 测试

成功部署后，您可以使用以下代码测试 API:

```javascript
// 在浏览器控制台或任何 JavaScript 环境中
fetch("http://localhost:8123/api/request/createRequest", {
  method: "POST",
  headers: { "Content-Type": "application/json" },
  body: JSON.stringify({
    type: "executeCode",
    lang: "mxs", // 使用 'mxs' 执行 MaxScript，或使用 'py' 执行 Python
    data: { code: "sphere radius:50 wirecolor:red" },
  }),
})
.then(response => response.json())
.then(data => console.log(data));
```

这个命令应该会在 3ds Max 中创建一个红色的球体。 