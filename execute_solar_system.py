import socket
import time
import os

def wait_for_server(max_attempts=10):
    """等待服务器启动"""
    for i in range(max_attempts):
        try:
            client = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            client.settimeout(2)
            client.connect(('127.0.0.1', 8123))
            client.close()
            return True
        except:
            print(f"等待服务器启动... ({i+1}/{max_attempts})")
            time.sleep(1)
    return False

def send_command(command):
    """发送命令到服务器"""
    try:
        client = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        client.settimeout(10)
        print("正在连接到3ds Max服务器...")
        client.connect(('127.0.0.1', 8123))
        print("连接成功")
        
        print("发送命令...")
        client.send(command.encode('utf-8'))
        
        print("等待响应...")
        response = client.recv(4096).decode('utf-8')
        print(f"收到响应: {response}")
        
        client.close()
        print("连接已关闭")
        return True
    except Exception as e:
        print(f"错误: {str(e)}")
        return False

def main():
    try:
        # 1. 首先启动服务器
        start_server_command = 'fileIn @"D:\\AI\\MCP_MAX\\start_tcp_server.ms"'
        print("\n=== 启动TCP服务器 ===\n")
        success = send_command(start_server_command)
        
        if not success:
            print("无法启动服务器")
            return
            
        # 2. 等待服务器完全启动
        print("\n=== 等待服务器就绪 ===\n")
        if not wait_for_server():
            print("服务器启动超时")
            return
            
        # 3. 读取并执行太阳系脚本
        print("\n=== 开始创建太阳系 ===\n")
        with open('create_solar_system.ms', 'r') as file:
            script = file.read()
            
        success = send_command(script)
        
        if success:
            print("\n太阳系创建完成！请在Max中：")
            print("1. 调整视图以查看整个系统")
            print("2. 打开时间滑块窗口（按键盘上的 'N' 键）")
            print("3. 点击播放按钮查看动画")
        
    except Exception as e:
        print(f"错误: {str(e)}")

if __name__ == "__main__":
    main()
