clearListener()

fn startSimpleServer port: 8123 = (
    try (
        -- 创建监听器
        local ipAddress = (dotNetClass "System.Net.IPAddress").Parse "127.0.0.1"
        local tcpListener = dotNetObject "System.Net.Sockets.TcpListener" ipAddress port
        tcpListener.Start()
        
        format "服务器已启动在端口 %\n" port
        messageBox ("服务器已启动在端口 " + port as string)
        
        -- 等待客户端连接
        format "等待客户端连接...\n"
        local clientSocket = tcpListener.AcceptSocket()
        format "客户端已连接\n"
        
        -- 接收数据
        local buffer = dotNetObject "System.Byte[]" 4096
        local bytesRead = clientSocket.Receive buffer
        
        if bytesRead > 0 then (
            -- 转换为字符串
            local encoding = dotNetClass "System.Text.UTF8Encoding"
            local command = trimright (encoding.UTF8.GetString buffer 0 bytesRead)
            format "收到命令: %\n" command
            
            -- 执行命令
            local result = execute command
            format "命令执行结果: %\n" (result as string)
            
            -- 发送响应
            local response = result as string
            local responseBytes = encoding.UTF8.GetBytes response
            clientSocket.Send responseBytes
            format "已发送响应\n"
        )
        
        -- 关闭连接
        clientSocket.Close()
        tcpListener.Stop()
        format "服务器已停止\n"
        
    ) catch (
        local errorMsg = "错误: " + (getCurrentException())
        format "%\n" errorMsg
        messageBox errorMsg title:"错误"
    )
)

-- 启动服务器
startSimpleServer() 