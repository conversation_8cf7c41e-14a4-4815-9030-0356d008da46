// 测试与3ds Max通信的简单脚本
import fetch from 'node-fetch';

async function testApi() {
  try {
    console.log('正在测试与3ds Max的通信...');
    
    const response = await fetch('http://localhost:8123/api/request/createRequest', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        type: 'executeCode',
        lang: 'mxs',
        data: { code: 'sphere radius:50 wirecolor:red' }
      })
    });
    
    const data = await response.json();
    console.log('API响应:');
    console.log(JSON.stringify(data, null, 2));
    
    if (data.requestId) {
      console.log(`\n通信成功! 请求ID: ${data.requestId}`);
      console.log('请检查3ds Max中是否创建了一个红色球体');
    } else {
      console.log('\n通信可能失败，请检查3ds Max是否正在运行以及服务器配置是否正确');
    }
  } catch (error) {
    console.error('测试失败:', error.message);
    console.log('\n请确保:');
    console.log('1. HTTP服务器运行在端口8123');
    console.log('2. 3ds Max正在运行且TCP服务器已启动在端口7603');
    console.log('3. 没有防火墙或其他网络问题阻止通信');
  }
}

// 执行测试
testApi(); 