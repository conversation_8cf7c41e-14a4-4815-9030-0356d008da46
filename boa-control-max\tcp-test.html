<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Max TCP直连测试</title>
    <style>
        body { font-family: 'Microsoft YaHei', Arial, sans-serif; padding: 20px; max-width: 800px; margin: 0 auto; }
        h1 { color: #333; }
        .control-panel { display: flex; flex-wrap: wrap; gap: 10px; margin: 20px 0; }
        button { 
            padding: 10px 15px; 
            background: #4CAF50; 
            color: white; 
            border: none; 
            border-radius: 4px; 
            cursor: pointer;
            min-width: 120px;
        }
        button:hover { background: #45a049; }
        .red { background: #f44336; }
        .red:hover { background: #d32f2f; }
        .blue { background: #2196F3; }
        .blue:hover { background: #1976D2; }
        .yellow { background: #FFC107; }
        .yellow:hover { background: #FFA000; }
        .purple { background: #9C27B0; }
        .purple:hover { background: #7B1FA2; }
        .console { 
            margin-top: 20px; 
            padding: 10px; 
            border: 1px solid #ddd; 
            background: #f5f5f5;
            height: 300px;
            overflow-y: auto;
            font-family: Consolas, monospace;
            white-space: pre-wrap;
        }
        .status {
            margin-top: 10px;
            padding: 5px 10px;
            border-radius: 4px;
            display: inline-block;
        }
        .status.connected { background: #4CAF50; color: white; }
        .status.disconnected { background: #f44336; color: white; }
    </style>
</head>
<body>
    <h1>3ds Max - TCP直连测试</h1>
    <div class="status disconnected" id="connection-status">未连接</div>

    <h2>创建几何体</h2>
    <div class="control-panel">
        <button class="red" onclick="sendCommand('box length:100 width:100 height:100 wirecolor:red name:&quot;TCP_RED_BOX&quot;')">红色盒子</button>
        <button class="blue" onclick="sendCommand('box length:80 width:80 height:150 wirecolor:blue name:&quot;TCP_BLUE_BOX&quot;')">蓝色盒子</button>
        <button class="yellow" onclick="sendCommand('sphere radius:50 wirecolor:yellow name:&quot;TCP_YELLOW_SPHERE&quot;')">黄色球体</button>
        <button class="purple" onclick="sendCommand('cylinder radius:40 height:120 wirecolor:purple name:&quot;TCP_PURPLE_CYLINDER&quot;')">紫色圆柱</button>
    </div>

    <h2>场景操作</h2>
    <div class="control-panel">
        <button onclick="sendCommand('max zoomextents all')">缩放至全部</button>
        <button onclick="sendCommand('max select $*')">选择全部</button>
        <button onclick="sendCommand('delete $')">删除选中项</button>
        <button class="red" onclick="sendCommand('delete objects')">清空场景</button>
    </div>

    <h2>通信日志</h2>
    <div class="console" id="console"></div>

    <script>
        let socket = null;
        let connected = false;
        const consoleElement = document.getElementById('console');
        const statusElement = document.getElementById('connection-status');

        // 记录日志到控制台区域
        function log(message, isError = false) {
            const timestamp = new Date().toLocaleTimeString();
            const logMessage = `[${timestamp}] ${message}`;
            consoleElement.innerHTML += (isError ? 
                `<div style="color: red">${logMessage}</div>` : 
                `<div>${logMessage}</div>`);
            consoleElement.scrollTop = consoleElement.scrollHeight;
        }

        // 连接到TCP服务器
        function connect() {
            // 对于网页直接调用TCP，需要一个中间件WebSocket服务
            // 这里假设WebSocket服务在相同端口，实际部署时需要调整
            log("尝试连接到TCP服务器...");
            try {
                // 使用原生TCP是不可能的，这里仅作为演示
                // 实际测试请使用Python脚本test-tcp-box.py或修改此代码使用WebSocket代理
                setTimeout(() => {
                    log("注意：浏览器不能直接连接TCP服务器", true);
                    log("请改用以下方法测试:");
                    log("1. 运行Python脚本test-tcp-box.py");
                    log("2. 使用PowerShell脚本direct-create-box.ps1");
                    log("3. 搭建WebSocket代理服务器");
                }, 1000);
            } catch (error) {
                log(`连接错误: ${error.message}`, true);
                updateConnectionStatus(false);
            }
        }

        // 发送命令到TCP服务器
        function sendCommand(mxsCode) {
            log(`准备发送命令: ${mxsCode}`);
            
            // 使用fetch发送到你的WebSocket代理服务器
            // 这里示意性地模拟命令执行
            log(`由于浏览器限制，无法直接发送TCP命令`);
            log(`请使用test-tcp-box.py脚本测试`);
            
            // 模拟响应
            setTimeout(() => {
                log(`模拟响应: {"executed": true, "result": "Ok"}`);
            }, 500);
        }

        // 更新连接状态UI
        function updateConnectionStatus(isConnected) {
            connected = isConnected;
            statusElement.textContent = isConnected ? "已连接" : "未连接";
            statusElement.className = isConnected ? "status connected" : "status disconnected";
        }

        // 页面加载时尝试连接
        window.onload = connect;
    </script>
</body>
</html> 