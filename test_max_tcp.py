import socket
import time

def send_command(command):
    try:
        client = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        client.settimeout(10)
        print(f"\n正在连接到服务器...")
        client.connect(('127.0.0.1', 8123))
        print("连接成功")
        
        print(f"发送命令...")
        client.send(command.encode('utf-8'))
        
        print("等待响应...")
        response = client.recv(4096).decode('utf-8')
        print(f"收到响应: {response}")
        
        client.close()
        print("连接已关闭")
        return True
        
    except Exception as e:
        print(f"错误: {str(e)}")
        return False

if __name__ == "__main__":
    # 创建太阳系统
    command = """
-- 设置场景单位为米
units.SystemType = #meters
units.DisplayType = #metric

-- 清除场景
delete objects

-- 创建太阳（中心黄色球体）
sun = Sphere radius:50 segments:32
sun.name = "Sun"
sun.wirecolor = color 255 255 0
sun.pos = [0,0,0]

-- 创建地球（蓝色球体）
earth = Sphere radius:20 segments:24
earth.name = "Earth"
earth.wirecolor = color 0 128 255
earth.pos = [200,0,0]

-- 创建月球（灰色球体）
moon = Sphere radius:5 segments:16
moon.name = "Moon"
moon.wirecolor = color 192 192 192
moon.pos = [250,0,0]

-- 创建轨道路径（辅助线）
earthOrbit = Circle radius:200 steps:48
earthOrbit.name = "EarthOrbit"
earthOrbit.wirecolor = color 128 128 128

moonOrbit = Circle radius:50 steps:32
moonOrbit.name = "MoonOrbit"
moonOrbit.wirecolor = color 128 128 128
moonOrbit.pos = earth.pos

-- 设置动画长度（10秒，300帧）
animationRange = interval 0 300
frameRate = 30

-- 地球绕太阳旋转动画
with animate on (
    -- 地球轨道动画
    for t in 0 to 300 by 1 do (
        at time t (
            local angle = (t * 360.0 / 300.0)
            earth.pos = [(200 * cos(angle)), (200 * sin(angle)), 0]
            -- 月球跟随地球
            moon.pos = earth.pos + [(50 * cos(angle * 12)), (50 * sin(angle * 12)), 0]
            -- 更新月球轨道位置
            moonOrbit.pos = earth.pos
        )
    )
)

-- 设置太阳自转
with animate on (
    for t in 0 to 300 by 1 do (
        at time t (
            sun.rotation.z_rotation = t * 2
        )
    )
)

-- 设置视图为透视图
viewport.setType #view_persp_user

"太阳系统创建完成！请在Max中：
1. 调整视图以查看整个系统
2. 打开时间滑块窗口（按键盘上的 'N' 键）
3. 点击播放按钮查看动画"
    """.strip()
    
    print("\n=== 创建太阳系统 ===")
    send_command(command) 