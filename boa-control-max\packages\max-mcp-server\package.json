{"name": "max-mcp-server", "version": "1.0.0", "description": "MCP server for Max integration", "main": "dist/server.js", "type": "module", "scripts": {"start": "tsx src/server.ts", "build": "tsc", "dev": "tsx watch src/server.ts"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@modelcontextprotocol/sdk": "^1.11.0", "eventsource": "^3.0.6", "zod": "^3.24.3"}, "devDependencies": {"@types/node": "^22.13.10", "tsx": "^4.19.4", "typescript": "^5.8.3"}}