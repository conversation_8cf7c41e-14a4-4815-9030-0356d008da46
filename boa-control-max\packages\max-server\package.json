{"name": "max-server", "version": "1.0.0", "description": "HTTP server", "main": "dist/server.js", "type": "module", "scripts": {"start": "tsx src/server.ts", "build": "tsc", "dev": "tsx watch src/server.ts"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"dotenv": "^16.4.7", "h3": "^1.15.1", "listhen": "^1.9.0", "pathe": "^2.0.3", "ws": "^8.16.0", "zod": "^3.24.2"}, "devDependencies": {"@types/node": "^22.13.10", "@types/ws": "^8.5.10", "tsx": "^4.7.1", "typescript": "^5.3.3"}}