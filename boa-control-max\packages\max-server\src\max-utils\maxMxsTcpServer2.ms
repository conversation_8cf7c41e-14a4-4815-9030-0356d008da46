-- maxMxsTcpServer.ms
-- Modified to resolve communication issues, ensure server startup, and improve debugging

clearListener()

-- 创建服务器结构
struct TCPServer (
    -- 公共属性
    port = 8123,
    
    -- 成员
    tcpListener = undefined,
    clientSocket = undefined,
    
    -- 启动服务器
    fn start = (
        try (
            -- 创建监听器
            local ipAddress = (dotNetClass "System.Net.IPAddress").Parse "127.0.0.1"
            format "调试: 解析 IP 地址: %\n" ipAddress
            tcpListener = dotNetObject "System.Net.Sockets.TcpListener" ipAddress port
            format "调试: 创建 tcpListener = %\n" tcpListener
            
            -- 尝试启动监听
            try (
                tcpListener.Start()
                format "服务器已启动在端口 %\n" port
                messageBox ("MaxMCP服务器已启动!\n监听地址: 127.0.0.1:" + port as string) title:"服务器启动"
                true
            ) catch (
                local errorMsg = "端口 % 可能被占用或无权限: %\n" port (getCurrentException())
                format "%\n" errorMsg
                messageBox errorMsg title:"错误"
                tcpListener = undefined
                false
            )
        ) catch (
            local errorMsg = "启动服务器失败: " + (getCurrentException())
            format "%\n" errorMsg
            messageBox errorMsg title:"错误"
            tcpListener = undefined
            false
        )
    ),
    
    -- 停止服务器
    fn stop = (
        if tcpListener != undefined do (
            try (
                tcpListener.Stop()
                format "服务器已停止\n"
            ) catch (
                format "停止监听器时出错: %\n" (getCurrentException())
            )
            tcpListener = undefined
        )
        if clientSocket != undefined do (
            try (
                clientSocket.Close()
            ) catch ()
            clientSocket = undefined
        )
    ),
    
    -- 处理客户端请求
    fn handleClient = (
        try (
            -- 检查是否有待处理的客户端连接（非阻塞）
            if tcpListener != undefined and tcpListener.Pending() then (
                format "调试: 检测到客户端连接...\n"
                clientSocket = tcpListener.AcceptSocket()
                format "调试: 客户端已连接: %\n" clientSocket
                
                -- 设置接收超时
                clientSocket.ReceiveTimeout = 5000 -- 5秒超时
                
                -- 接收数据
                local buffer = dotNetObject "System.Byte[]" 4096
                local bytesRead = clientSocket.Receive buffer
                
                if bytesRead > 0 then (
                    -- 转换为字符串
                    local encoding = dotNetClass "System.Text.UTF8Encoding"
                    local command = trimright (encoding.UTF8.GetString buffer 0 bytesRead)
                    format "调试: 收到命令: %\n" command
                    
                    -- 执行命令
                    local result = undefined
                    try (
                        result = execute command
                        format "调试: 命令执行成功: %\n" (result as string)
                    ) catch (
                        local errorMsg = "Error: " + (getCurrentException())
                        format "调试: 命令执行失败: %\n" errorMsg
                        result = errorMsg
                    )
                    
                    -- 发送响应
                    local response = (result as string) + "\n" -- 添加换行符确保客户端接收完整
                    local responseBytes = encoding.UTF8.GetBytes response
                    clientSocket.Send responseBytes
                    format "调试: 已发送响应: %\n" response
                ) else (
                    format "调试: 未接收到数据\n"
                )
                
                -- 关闭连接
                clientSocket.Close()
                clientSocket = undefined
                format "调试: 客户端连接已关闭\n"
            )
        ) catch (
            format "调试: 处理客户端时出错: %\n" (getCurrentException())
            if clientSocket != undefined do (
                try (
                    clientSocket.Close()
                ) catch ()
                clientSocket = undefined
            )
        )
    ),
    
    -- 主循环（由定时器调用）
    fn run = (
        if tcpListener != undefined do (
            handleClient()
        )
    )
)

-- 创建并启动服务器
global gTCPServer = TCPServer()
if gTCPServer.start() then (
    -- 创建简单 UI 控制服务器
    try (destroyDialog serverControlRollout) catch ()
    rollout serverControlRollout "MaxMCP Server Control" width:200 height:100 (
        button btnStop "Stop Server" pos:[10,10] width:180 height:30
        label lblStatus "Server Running on port 8123" pos:[10,50] width:180 height:20
        
        on btnStop pressed do (
            if gTCPServer != undefined do (
                gTCPServer.stop()
                if serverTimer != undefined do (
                    serverTimer.Stop()
                    serverTimer = undefined
                )
                lblStatus.text = "Server Stopped"
                messageBox "服务器已停止" title:"服务器控制"
            )
        )
    )
    
    -- 使用定时器异步运行服务器主循环
    global serverTimer = dotNetObject "System.Windows.Forms.Timer"
    serverTimer.Interval = 200 -- 调整到200ms，平衡响应性和性能
    fn serverTick = (
        if gTCPServer.tcpListener != undefined then (
            gTCPServer.run()
            windows.processPostedMessages() -- 强制处理界面消息
            redrawViews() -- 强制刷新视图
        ) else (
            serverTimer.Stop()
            format "调试: 服务器已停止运行\n"
            if serverControlRollout != undefined do (
                serverControlRollout.lblStatus.text = "Server Stopped"
            )
        )
    )
    dotNet.addEventHandler serverTimer "Tick" serverTick
    serverTimer.Start()
    format "调试: 服务器主循环已通过定时器启动\n"
    
    -- 显示控制面板
    createDialog serverControlRollout
) else (
    format "调试: 服务器启动失败，请检查端口或错误日志\n"
)