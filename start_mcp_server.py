import os
import sys
import time

def start_max_server():
    """启动Max TCP服务器"""
    server_path = os.path.join(os.path.dirname(__file__), 
                              'boa-control-max', 'packages', 
                              'max-server', 'src', 'max-utils', 
                              'maxPyTcpServer.py')
    
    if os.path.exists(server_path):
        print(f"加载服务器脚本: {server_path}")
        with open(server_path, 'r', encoding='utf-8') as f:
            server_code = f.read()
            
        # 执行服务器代码
        exec(server_code, globals())
        print("TCP服务器启动成功!")
        return True
    else:
        print(f"错误: 找不到服务器脚本: {server_path}")
        return False

if __name__ == "__main__":
    print("\n=== 启动Max TCP服务器 ===\n")
    if start_max_server():
        print("\n服务器已启动，现在可以运行太阳系创建脚本了。")
    else:
        print("\n服务器启动失败，请检查配置。")
