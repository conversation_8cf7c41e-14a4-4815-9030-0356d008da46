import socket
import json
import time

def test_max_server(retries=3):
    print("开始测试MaxScript服务器...")
    
    for attempt in range(retries):
        if attempt > 0:
            print(f"\n第 {attempt + 1} 次尝试...")
            time.sleep(2)  # 等待一会再重试
        
        # 创建socket连接
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(30)  # 设置30秒超时
        
        try:
            # 连接到服务器
            print("正在连接到服务器...")
            sock.connect(('localhost', 8123))
            print("连接成功！")
            
            # 创建测试命令
            command = 'box length:50 width:50 height:50 wirecolor:red name:"TEST_BOX"'
            
            # 发送命令
            print("发送命令：创建红色方块...")
            sock.sendall(command.encode('utf-8'))
            
            # 接收响应
            print("等待响应...")
            response = ""
            while True:
                try:
                    data = sock.recv(8192)
                    if not data:
                        break
                    response += data.decode('utf-8')
                except socket.timeout:
                    print("接收响应超时")
                    break
            
            print("服务器响应:", response)
            
            if "Error" not in response:
                print("\n测试成功！请检查3ds Max中是否创建了一个红色方块。")
                return True
            else:
                print(f"\n测试失败：{response}")
                
        except ConnectionRefusedError:
            print("\n连接被拒绝：请确保3ds Max正在运行且服务器已启动")
        except socket.timeout:
            print("\n连接超时：请检查服务器是否正常运行")
        except Exception as e:
            print(f"\n测试出错：{str(e)}")
        finally:
            try:
                sock.close()
            except:
                pass
            
    print("\n所有重试都失败了")
    return False

if __name__ == "__main__":
    test_max_server()
    input("\n按Enter键退出...") 