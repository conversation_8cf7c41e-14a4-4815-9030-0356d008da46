import sys
import json
import time
import socket
import math
from pathlib import Path

def execute_mxs_code(code):
    """通过MCP服务器执行MaxScript代码"""
    data = {
        'type': 'execute',
        'lang': 'mxs',
        'code': code,  # MaxScript代码已经在调用处用括号包装
        'requestId': f'solar_system_{int(time.time())}'
    }
    
    try:
        # 使用TCP连接到Max服务器
        client = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        client.settimeout(10)
        client.connect(('127.0.0.1', 8123))
        
        # 发送请求
        request = json.dumps(data)
        client.send(request.encode('utf-8'))
        
        # 接收响应
        response = client.recv(4096).decode('utf-8')
        client.close()
        
        result = json.loads(response)
        if isinstance(result, dict) and 'error' in result:
            return result
        return {'status': 'success', 'result': result}
    except Exception as e:
        return {'error': str(e)}

def create_solar_system():
    """创建太阳系场景"""
    # 创建MaxScript代码
    maxscript_code = """
    -- Reset the scene
    resetMaxFile #noPrompt
    
    -- Create Sun
    sun = Sphere radius:20 segments:32
    sun.name = "Sun"
    sun.position = [0, 0, 0]
    
    -- Create yellow material for Sun
    sun_material = StandardMaterial()
    sun_material.diffuse = (color 255 255 0)
    sun_material.selfIllumAmount = 100
    sun.material = sun_material
    
    -- Create Earth
    earth = Sphere radius:8 segments:32
    earth.name = "Earth"
    earth.position = [100, 0, 0]
    
    -- Create blue material for Earth
    earth_material = StandardMaterial()
    earth_material.diffuse = (color 0 100 255)
    earth.material = earth_material
    
    -- Create Moon
    moon = Sphere radius:3 segments:32
    moon.name = "Moon"
    moon.position = [130, 0, 0]
    
    -- Create grey material for Moon
    moon_material = StandardMaterial()
    moon_material.diffuse = (color 200 200 200)
    moon.material = moon_material
    
    -- Set up animation
    animationRange = interval 0 300
    
    -- Create animation
    for t = 0 to 300 do
    (
        with animate on
        (
            at time t
            (
                -- Earth orbit
                earth_angle = (t as float / 300.0) * 360
                earth_x = 100 * cos(earth_angle)
                earth_z = 100 * sin(earth_angle)
                earth.position = [earth_x, 0, earth_z]
                
                -- Moon orbit (3x faster than Earth)
                moon_angle = earth_angle * 3
                moon_offset_x = 30 * cos(moon_angle)
                moon_offset_z = 30 * sin(moon_angle)
                moon.position = [earth_x + moon_offset_x, 0, earth_z + moon_offset_z]
            )
        )
    )
    
    -- Add camera
    cam = FreeCamera()
    cam.name = "SolarSystemCamera"
    cam.position = [0, -300, 200]
    if cam.target != undefined do cam.target.position = [0, 0, 0]
    
    -- Add light
    sun_light = Omnilight()
    sun_light.name = "SunLight"
    sun_light.position = [0, 0, 0]
    sun_light.multiplier = 2
    
    -- Set viewport to use the new camera
    viewport.setCamera cam

    -- Return success message
    "Solar system created successfully!"
    """
      -- Return result using Python OrderedDict for proper JSON serialization
    local pcols = python.import "collections"
    local result = pcols.OrderedDict()
    result["status"] = "success"
    result["message"] = "Solar system created successfully"
    
    local pbi = python.import "builtins"
    local objects = pbi.list()
    objects.append "Sun"
    objects.append "Earth"
    objects.append "Moon"
    objects.append "SolarSystemCamera"
    objects.append "SunLight"
    result["objects"] = objects
    
    result
    """
    
    # 执行代码并返回结果
    return execute_mxs_code(maxscript_code)

if __name__ == "__main__":
    print("\n=== 通过MCP服务创建太阳系 ===\n")
    result = create_solar_system()
    
    print("执行结果:")
    print(json.dumps(result, indent=2, ensure_ascii=False))
    
    if 'error' not in result:
        print("\n太阳系创建成功！请在Max中：")
        print("1. 调整视图以查看整个系统")
        print("2. 打开时间滑块窗口（按键盘上的 'N' 键）")
        print("3. 点击播放按钮查看动画")
    else:
        print("\n创建失败，错误信息：")
        print(result['error'])
