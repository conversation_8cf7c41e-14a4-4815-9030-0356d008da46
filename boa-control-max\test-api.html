<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BOA Control Max API 测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .card {
            background-color: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background-color: #45a049;
        }
        #result {
            white-space: pre-wrap;
            background-color: #f8f8f8;
            border: 1px solid #ddd;
            padding: 10px;
            border-radius: 4px;
            overflow: auto;
            max-height: 300px;
        }
        .input-section {
            margin-bottom: 15px;
        }
        textarea {
            width: 100%;
            height: 80px;
            margin-top: 5px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: monospace;
        }
        label {
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>BOA Control Max API 测试</h1>
    
    <div class="card">
        <h2>创建一个红色球体</h2>
        <div class="input-section">
            <label for="maxscript">MaxScript 代码:</label>
            <textarea id="maxscript">sphere radius:50 wirecolor:red</textarea>
        </div>
        <button onclick="testCreateSphere()">执行</button>
        <div id="result" style="margin-top: 15px;"></div>
    </div>

    <div class="card">
        <h2>测试状态</h2>
        <div>
            <button onclick="testHttpServer()">测试 HTTP 服务器</button>
            <button onclick="checkServerInfo()">获取服务器信息</button>
        </div>
        <div id="statusResult" style="margin-top: 15px; white-space: pre-wrap;"></div>
    </div>

    <script>
        function displayResult(data, element = 'result') {
            document.getElementById(element).textContent = typeof data === 'object' 
                ? JSON.stringify(data, null, 2) 
                : data;
        }

        // 测试创建球体
        async function testCreateSphere() {
            const code = document.getElementById('maxscript').value;
            displayResult('正在发送请求...');
            
            try {
                const response = await fetch('http://localhost:8123/api/request/createRequest', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        type: 'executeCode',
                        lang: 'mxs',
                        data: { code }
                    })
                });
                
                const data = await response.json();
                displayResult(data);
                
                if (data.requestId) {
                    alert('请求成功发送！请检查 3ds Max 中是否创建了球体。');
                }
            } catch (error) {
                displayResult(`错误: ${error.message}\n\n请确保:\n1. HTTP服务器运行在端口8123\n2. 3ds Max正在运行且TCP服务器已启动在端口7603`);
            }
        }

        // 测试HTTP服务器连接
        async function testHttpServer() {
            displayResult('正在测试HTTP服务器连接...', 'statusResult');
            
            try {
                const response = await fetch('http://localhost:8123/', {
                    method: 'GET'
                });
                
                displayResult(`HTTP服务器状态: ${response.status} ${response.statusText}`, 'statusResult');
            } catch (error) {
                displayResult(`HTTP服务器连接失败: ${error.message}`, 'statusResult');
            }
        }

        // 检查服务器信息
        function checkServerInfo() {
            const info = `
服务器配置信息:
---------------------
HTTP服务器: http://localhost:8123/
TCP服务器(3ds Max): localhost:7603

确保:
1. HTTP服务器已启动 (npx pnpm run dev)
2. 3ds Max中TCP服务器已启动 (运行了简化版TCP服务器.py)
3. 没有防火墙阻止通信
            `;
            
            displayResult(info, 'statusResult');
        }

        // 页面加载时显示服务器信息
        window.onload = checkServerInfo;
    </script>
</body>
</html> 